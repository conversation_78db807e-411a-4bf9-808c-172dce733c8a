---
type: "always_apply"
---

# AI Pair Programming Rulebook — General

This file defines organization-wide rules. Platform-specific rules live in [web-rules.md](./web-rules.md) and [mobile-rules.md](./mobile-rules.md). Monorepo-only policies live in [monorepo-rules.md](./monorepo-rules.md).

- Do not duplicate content across files.
- When in doubt, reference the relevant file instead of restating.

## 1) 95% Clarification Protocol

Before implementing a new feature or significant change:
- Ask clarifying questions until you are ≥95% confident about scope, constraints, and acceptance criteria.
- Summarize the plan (what/why/how, risks, alternatives) and list any remaining assumptions.
- If assumptions remain, request explicit approval before coding.
- For ambiguous tickets, propose 2–3 options with trade-offs and recommend one.

## 2) Research and Citations (Context7 MCP)

- Always research dependencies/libraries with Context7 before adding or upgrading.
- Verify:
  - Compatibility with current stack (Node 20, TypeScript strict, Next.js App Router, Expo SDK latest stable).
  - Maintenance/health (recent releases, docs, trust score).
  - Security and licensing.
- In PR descriptions:
  - Paste relevant doc links and short excerpts.
  - Note version constraints and migration notes.

## 3) Repository and Workflow (GitHub)

- Branches:
  - `main` is the release branch. Use feature branches (e.g., `feat/login-oauth`) for changes.
  - Even when solo, keep `main` protected (no force-push).
- Commits and releases:
  - Use Conventional Commits (e.g., `feat:`, `fix:`, `chore:`).
  - Use Changesets to generate changelogs and bump versions.
- Pull Requests:
  - Template should include:
    - Summary, screenshots (if UI), performance notes.
    - Context7 citations for new/updated deps.
    - Checklist: lint, typecheck, tests passed; no secrets; docs updated.
- CI (GitHub Actions):
  - Required checks: lint, typecheck, tests, build (as applicable).
  - For monorepos, run affected-only tasks (see [monorepo-rules.md](./monorepo-rules.md)).

## 4) Security and Secrets

- Never commit secrets. Use environment variables and encrypted GitHub Secrets.
- Minimal permissions:
  - Only request platform permissions you actually use (mobile).
  - Harden headers/CSP (web, see [web-rules.md](./web-rules.md)).
- Dependency hygiene:
  - Keep lockfile committed. Avoid unpinned unstable ranges.
  - Enable automated dependency updates (Renovate/Dependabot).
- Monitoring:
  - Use Sentry for web and mobile. Configure DSNs per environment without committing keys.

## 5) Quality Gates

- Language and types:
  - Node 20 LTS. TypeScript strict mode on.
- Static analysis and formatting:
  - ESLint with recommended/strict rules for the stack.
  - Prettier for formatting.
  - Pre-commit hooks: lint, typecheck, tests.
- Testing:
  - Unit tests for core logic.
  - Integration/e2e where it provides clear value (see platform specifics).
  - Keep CI green; do not merge red builds.

### Styling Consistency (Org-wide)

- Use shared design tokens as the single source of truth (colors, spacing, radii, typography, z-index).
- Do not use raw values (hex colors, pixel numbers) in component styles; always reference tokens.
- Enforce via lint/CI:
  - Web: Prettier Tailwind plugin and eslint-plugin-tailwindcss.
  - Mobile: disallow raw inline `style` values; prefer Gluestack tokens and variants.
- For mobile Gluestack patterns and composition, see [gluestack-best-practices.md](./gluestack-best-practices.md).
- PR checklist must include “All styles use tokens.”

## 6) Observability

- Logging levels: debug, info, warn, error. Avoid logging secrets.
- Sentry:
  - Separate projects per app.
  - Tag releases and environment (dev/preview/staging/prod).
  - Redact PII where necessary.

## 7) Documentation

- Keep a top-level README describing architecture, setup, and scripts.
- Use brief ADRs for impactful decisions (1–2 pages, problem → options → decision → consequences).
- Keep app/package-level READMEs up to date (install/run/test instructions).

## 8) Cross-References

- Web rules: see [web-rules.md](./web-rules.md).
- Mobile rules: see [mobile-rules.md](./mobile-rules.md).
- Monorepo rules: see [monorepo-rules.md](./monorepo-rules.md).
- Gluestack UI best practices: see [gluestack-best-practices.md](./gluestack-best-practices.md).