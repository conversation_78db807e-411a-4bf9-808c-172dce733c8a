---
type: "agent_requested"
description: "Example description"
---
# Gluestack UI Best Practices (React Native / Expo)

This guide summarizes practical patterns and pitfalls for using Gluestack UI in our Expo mobile app, aligned with our org rules (design tokens, TypeScript strict, accessibility, and performance). References are included at the end.

## Prerequisites
- Install: `@gluestack-ui/themed` and `@gluestack-ui/config`.
- Wrap the app root with `GluestackUIProvider` (e.g., in `apps/mobile/App.tsx`).
- Enable TypeScript strict mode for best type safety.

## 1) Provider & Configuration
- __Use the official config__: Wrap the app with `Gluestack<PERSON><PERSON>rovider` and pass `config` from `@gluestack-ui/config`.
- __Single provider at root__: Place it in `apps/mobile/App.tsx` so all screens share the theme.
- __Theme tokens over raw values__: Prefer tokens in props (e.g., `color="$white"`, `mb="$6"`) to keep consistency with our design system.

Example:
```tsx
import { GluestackUIProvider } from '@gluestack-ui/themed'
import { config } from '@gluestack-ui/config'

export function Providers({ children }: { children: React.ReactNode }) {
  return <GluestackUIProvider config={config}>{children}</GluestackUIProvider>
}
```

See References & Citations below for GluestackUIProvider with `config`.

## 2) Slot Components (Composition)
- __Buttons__: Use `ButtonText` as the child of `Button` (not `Text`).
- __Radios__: Compose `Radio` with `RadioIndicator`, `RadioIcon`, and `RadioLabel` inside a `RadioGroup`.
- __Checkboxes__: Compose `Checkbox` with `CheckboxIndicator`, `CheckboxIcon`, and `CheckboxLabel` inside a `CheckboxGroup`.
- __FormControl__: Wrap inputs with `FormControl`, `FormControlLabel`, `FormControlLabelText`, and (optionally) helper/error components.

Examples:
```tsx
// Button
<Button action="primary"><ButtonText>Continue</ButtonText></Button>

// Radio
<RadioGroup value={value} onChange={setValue}>
  <Radio value="optA">
    <RadioIndicator>
      <RadioIcon />
    </RadioIndicator>
    <RadioLabel>Option A</RadioLabel>
  </Radio>
</RadioGroup>

// Checkbox
<CheckboxGroup value={values} onChange={setValues}>
  <Checkbox value="opt1">
    <CheckboxIndicator>
      <CheckboxIcon />
    </CheckboxIndicator>
    <CheckboxLabel>Opt 1</CheckboxLabel>
  </Checkbox>
</CheckboxGroup>

// Input inside FormControl
<FormControl>
  <FormControlLabel>
    <FormControlLabelText>Age</FormControlLabelText>
  </FormControlLabel>
  <Input>
    <InputField keyboardType="numeric" />
  </Input>
</FormControl>
```

See References & Citations below for ButtonText example and Radio/Checkbox composition.

## 3) TypeScript & Strictness
- __Exact optional props__: Some libraries (e.g., React Navigation v7) use strict optional typing. If `Stack.Navigator` requires `id`, pass `id={undefined}` when needed to satisfy types (this stems from strict optional prop types in upstream definitions).
- __Children types__: Follow Gluestack’s slot components; incorrect children (e.g., `Text` inside `Button`) cause TS errors.
- __Input values__: Convert string inputs to numbers before saving to typed stores (e.g., `Number(age)` for `age?: number`).

## 4) Tokens and Styling
- __No raw values__: Use tokens from `packages/tokens/` and Gluestack props like `px="$4"`, `color="$textLight0"`, `rounded="$lg"`.
- __Variant-first styling__: Prefer component variants and state props over inline styles. Keep overrides minimal to preserve theme consistency.
- __Consistent spacing and typography__: Ensure spacing/typography is driven by tokens and used consistently across screens.

- __Color mode__: Ensure tokens define accessible light/dark variants and verify contrast.

Example: extending the default config with brand tokens:
```ts
import { config as baseConfig } from '@gluestack-ui/config'

export const config = {
  ...baseConfig,
  tokens: {
    ...baseConfig.tokens,
    colors: {
      ...baseConfig.tokens?.colors,
      brand100: '#E6F0FF',
      brand500: '#3B82F6',
    },
  },
}
```
Then pass this `config` to `GluestackUIProvider`.

## 5) Accessibility (A11y)
- __Labels__: Always provide readable labels with `RadioLabel` / `CheckboxLabel` and `FormControlLabelText`.
- __State announcements__: Gluestack components support screen readers; ensure checked/unchecked is conveyed via proper composition.
- __Focus management__: Use built-in focus states; avoid removing focus outlines. Prefer `onFocus`/`onBlur` only for logic, not to hide focus.

## 6) Performance & Footprint
- __Composition over custom__: Reuse provided primitives; avoid reimplementing state styles.
- __Avoid heavy inline styles__: Stick with themed props for zero-runtime class generation and better caching.
- __Batch re-renders__: Keep forms controlled but avoid unnecessary state churn; memoize expensive subtrees if needed.

## 7) Forms & Validation Patterns
- __Use `FormControl` wrappers__: For each field, include label, control, and error/helper text if applicable.
- __Native keyboard types__: Set `keyboardType` (numeric/email) and validate accordingly.
- __Data normalization__: Cast to correct types when persisting (e.g., numbers for numeric fields).

## 8) Navigation & Layout Integration
- __Navigator typing__: With strict TS settings, provide required props (`id={undefined}` workaround) to avoid type errors.
- __Layout spacing__: Use `VStack`/`HStack` with tokenized spacing (`space="md"`) rather than manual margins.
- __Header & theming__: Keep screen titles and header visibility in navigator options; don’t embed faux headers in content.

## 9) Testing Tips
- __@testing-library/react-native__: Query by role/label where possible (labels from `*Label` components).
- __Snapshot minimalism__: Prefer interaction tests over large snapshots; assert state changes via accessibility states (checked, disabled).

## 10) Common Pitfalls
- __Using plain Text in Button__: Always use `ButtonText`.
- __Missing provider config__: Ensure `@gluestack-ui/config` is installed and used at the root.
- __Bypassing tokens__: Avoid raw hex/pixel values; use tokens.
- __Incorrect composition__: Radio/Checkbox must include Indicator/Icon/Label as children; otherwise styles and a11y degrade.

## 11) Versioning & API Strategy
- __Keep packages aligned__: Update `@gluestack-ui/themed` and `@gluestack-ui/config` together to avoid theme/token drift.
- __Don’t mix approaches in one app__: Avoid mixing “themed” components with copy-paste (NativeWind className) variants in the same feature. Standardize on one to prevent duplication and styling divergence.

## 12) Variants & Design System Guardrails
- __Standardize variants/sizes__: Document allowed `action`, `variant`, and `size` for primitives like `Button`, `Input`, `Badge`.
- __Enforce via tokens__: Map variants to tokens; keep overrides minimal so teams can’t bypass design decisions.

## 13) Form Semantics & Error Handling
- __Always wrap fields in `FormControl`__: Include `FormControlLabel`/`FormControlLabelText`, helper, and error text when relevant.
- __Error semantics__: When invalid, set input error state and surface error text; connect using the control’s error composition so screen readers can announce it (see Input accessibility guidance).
- __Required fields__: Mark visually and set required state where appropriate so assistive tech can convey it.

## 14) Accessibility Details (Labels, Focus, Keyboard, Hit Areas)
- __Labels__: Prefer slot labels (`RadioLabel`, `CheckboxLabel`, `FormControlLabelText`) over plain `Text`.
- __Focus__: Keep default focus outlines; use `onFocus`/`onBlur` for logic only, not to hide focus.
- __Keyboard__: Ensure keyboard actions are reachable; respect platform conventions.
- __Touch targets__: Ensure ~44×44pt minimum for interactive areas (e.g., padding on `InputSlot` buttons).

## 15) Inputs & Icons Patterns
- __Use `InputSlot` + `InputIcon`__: Prefer built-in slots over absolute-positioned icons for reliable theming and accessibility.
- __Password toggles__: Implement with an `InputSlot` pressable and swap icons; ensure the slot is keyboard/touch accessible.

## 16) RTL Readiness
- __Mirroring__: Ensure directional icons/chevrons mirror in RTL. Prefer flex-based layout and token spacing instead of absolute positioning.

## 17) Testing Patterns
- __Accessibility-first queries__: In `@testing-library/react-native`, query by role/label and assert accessibility states (checked/disabled/invalid) for Radios/Checkboxes and inputs.
- __Minimal snapshots__: Prefer interaction tests over large snapshots.

## 18) Performance Hygiene
- __Token props over inline styles__: Favor Gluestack props backed by tokens (zero-runtime class generation) over heavy inline styles.
- __Memoize deliberately__: Memoize expensive subtrees; avoid blanket memoization.

## 19) Quick Do/Don’t
- __Do__: `Button` + `ButtonText`.
- __Don’t__: `Button` + `Text`.
- __Do__: `FormControl` with label + error.
- __Don’t__: Standalone input with disconnected error string.
- __Do__: `InputSlot` icons.
- __Don’t__: Absolutely positioned icons.

---
## References & Citations
- Gluestack UI Getting Started and Provider setup:
  - GluestackUIProvider with `config` (themed README):
    - https://github.com/gluestack/gluestack-ui/blob/main/packages/themed/README.md#_snippet_1
  - Config package basic setup:
    - https://github.com/gluestack/gluestack-ui/blob/main/packages/config/README.md#_snippet_1
  - Introduction (theming tokens and config):
    - https://gluestack.io/ui/docs/home/<USER>/introduction
- Components and composition patterns:
  - All components:
    - https://gluestack.io/ui/docs/components/all-components
  - Checkbox (Indicator/Icon/Label, within Group):
    - https://gluestack.io/ui/docs/components/checkbox
  - Radio (Indicator/Icon/Label, within Group):
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/components/Radio/index.nw.stories.mdx#_snippet_0
  - Tooltip example showing Button + ButtonText:
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/components/Tooltip/index.themed.stories.mdx#_snippet_0
- Accessibility and states:
  - Radio screen reader/focus notes:
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/components/Radio/index.nw.stories.mdx#_snippet_8
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/components/Radio/index.nw.stories.mdx#_snippet_9
- Performance benchmarks:
  - Overview benchmarks:
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/home/<USER>/benchmarks/index.stories.mdx#_qa_5

Short excerpts:
- “Write this code snippet at the root of your application … `<GluestackUIProvider config={config}>`” (themed README).
- “Theming Tokens: gluestack-ui takes a config file in the GluestackUIProvider, which contains theme tokens … You can install and import the config as a module.” (Introduction).
- “Checkbox … contains all Checkbox related layout style props and actions … Inherits properties of React Native’s View component.” (Checkbox docs).
