version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    groups:
      dependencies:
        patterns:
          - "*"
    ignore:
      - dependency-name: "react"
        versions: ["19.x"]
      - dependency-name: "react-dom"
        versions: ["19.x"]
      - dependency-name: "next"
        versions: ["15.x"]
      - dependency-name: "expo"
        versions: ["52.x"]
