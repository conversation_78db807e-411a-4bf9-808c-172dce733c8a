name: CD

on:
  workflow_dispatch:
    inputs:
      deploy_admin:
        description: 'Deploy admin panel to Cloudflare Pages'
        required: false
        default: true
        type: boolean
      deploy_functions:
        description: 'Deploy functions to Cloudflare Workers'
        required: false
        default: false
        type: boolean
  # Temporarily disabled automatic deployment until Cloudflare projects are set up
  # push:
  #   branches: [ main ]

jobs:
  deploy-admin:
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.deploy_admin == 'true' }}
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile
      - name: Build admin panel
        run: pnpm turbo build --filter=@active-bite-ai/admin
      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: active-bite-ai-admin
          directory: apps/admin/out
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

  deploy-functions:
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.deploy_functions == 'true' }}
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile
      - name: Install Wrangler
        run: pnpm add -w wrangler@latest
      - name: Deploy to Cloudflare Workers
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          wranglerVersion: '3'
