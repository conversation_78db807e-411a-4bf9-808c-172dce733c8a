---
trigger: manual
---

# Mobile Rules — Expo + React Native + Gluestack (formerly NativeBase)

This file defines mobile-only guidance. Do not restate general rules or monorepo policies; reference [general.md](./general.md) and [monorepo.md](./monorepo.md) instead.

## 1) Stack

- React Native via Expo (SDK: latest stable).
- JavaScript runtime: Hermes enabled.
- Routing: `expo-router`.
- UI: Gluestack (formerly NativeBase). Use Gluestack components, tokens, and theming.
- State/data: Choose per project (e.g., React Query, Zustand) and document in README.

## 2) Project Structure

- `app/` — routes using `expo-router` (file-based routing).
- `components/` — shared UI components.
- `features/` — feature modules (UI + logic + tests).
- `lib/` — platform services (api, storage, analytics).
- `hooks/` — shared hooks.
- `assets/` — images, fonts.
- `theme/` — Gluestack theme and tokens.

## 3) Theming and Accessibility

- Define design tokens in Gluestack theme (spacing, color, typography).
- Support color mode (light/dark) and ensure sufficient contrast.
- Accessibility:
  - Use accessible components and props (labels, roles).
  - Ensure focus management and larger text settings compatibility.
  - Test with screen readers (VoiceOver/TalkBack) for critical flows.

### Enforcement

- Consume shared tokens from the design tokens package; do not hardcode hex colors, spacing numbers, or radii.
- Use Gluestack components and variants; avoid inline `style` objects with raw values.
- Any necessary exception must be documented in the PR with rationale and follow-up to migrate to tokens.

## 4) Performance

- Enable Hermes.
- Use React Native Reanimated for performant animations/gestures.
- Lists:
  - Use FlashList (or FlatList with correct `getItemLayout`, `keyExtractor`).
  - Avoid nested lists; paginate or virtualize when needed.
- Images:
  - Use `expo-image` with caching.
  - Preload critical assets during splash.
- Rendering:
  - Memoize heavy components (`memo`, `useMemo`, `useCallback`).
  - Avoid expensive work on the JS thread; offload where possible.

## 5) Configuration and EAS

- `app.config.ts`:
  - Set `updates.runtimeVersion` policy (e.g., appVersion policy) to support OTA safely.
  - Set app name, icons, splash, bundle identifiers/package names.
- EAS Build and Submit:
  - Channels: `preview`, `staging`, `production`.
  - Build profiles per env; submit to stores from CI when ready.
- OTA Updates with `eas update`:
  - Use channels to promote builds (preview → staging → production).
  - Rollback by republishing a known-good runtime.

## 6) Testing

- Unit/integration:
  - Jest + React Native Testing Library for components and hooks.
  - Mock native modules and network calls.
- e2e (optional for critical user journeys):
  - Detox with a small set of high-value scenarios.
- Keep tests deterministic and fast; avoid device flakiness where possible.

## 7) Release and Monitoring

- Stores: prepare metadata, privacy manifest (iOS), and screenshots.
- Crash/error reporting: Sentry
  - Tag releases and environment.
  - Symbolication set up in CI (dSYMs/ProGuard mappings as applicable).
- Feature flags:
  - Use flags for risky changes; keep rollout controlled.

## 8) Security

- Request minimal permissions; document justification.
- Use secure storage for secrets/tokens.
- Validate and sanitize inputs; handle deep links safely.
- Keep dependencies current and reviewed (see [general.md](./general.md)).

## 9) Cross-References

- General engineering standards: see [general.md](./general.md).
- Monorepo tooling/pipelines: see [monorepo.md](./monorepo.md).