---
trigger: always_on
---

# Monorepo Rules — Turborepo + pnpm + Changesets

This file defines monorepo-only policies. Do not restate platform rules; reference [web.md](./web.md) and [mobile.md](./mobile.md) instead.

## 1) When to Use

- Multiple apps (`apps/`) and shared packages (`packages/`) that benefit from code sharing, unified tooling, and cached pipelines.

## 2) Structure

- `apps/` — e.g., `web`, `mobile`.
- `packages/` — e.g., `ui`, `config`, `tsconfig`, `eslint-config`, `utils`.
- Shared TypeScript config via project references.
- Shared ESLint/Prettier configs to enforce consistency.

## 3) Design Tokens

- `packages/tokens/` is the single source of truth for design tokens (colors, spacing, radii, typography, z-index).
- Export tokens in TypeScript (and optionally JSON) for cross-platform consumption.
- Apps must consume tokens rather than hardcoding raw values. Enforcement happens via shared ESLint/Prettier and CI.
- Root config should include Prettier Tai<PERSON>wind plugin (web) and shared ESLint rules that disallow raw style values (see platform files).

## 4) Package Management

- pnpm workspaces with a single lockfile.
- Enforce Node 20 engine and consistent scripts across packages.
- Avoid circular deps; keep dependency graphs simple.

## 5) Turborepo Pipelines

- Define tasks per package (e.g., `build`, `dev`, `lint`, `typecheck`, `test`).
- Leverage hashing-based caching and (optionally) remote caching.
- Use pipeline dependencies (e.g., `build` depends on `typecheck`) to enforce order.
- Affected-only runs in CI to speed up checks.

## 6) Versioning and Releases

- Use Changesets:
  - Conventional Commits for meaningful change types.
  - Generate changelogs per package.
  - Publish/release only changed packages.
- Tag releases per app/package when appropriate.

## 7) CI (GitHub Actions)

- Jobs:
  - Install (pnpm), lint, typecheck, test, build.
  - Use workspace caching for pnpm.
- Affected-only steps using Turborepo to reduce CI time.
- Separate preview/staging/production deployment steps per app.

## 8) Cross-References

- General engineering standards: see [general.md](./general.md).
- Platform-specific rules: see [web.md](./web.md) and [mobile.md](./mobile.md).