---
trigger: manual
---

# Web Rules — Next.js + Tailwind v4 + shadcn/ui

This file defines web-only guidance. Do not restate general rules or monorepo policies; reference [general.md](./general.md) and [monorepo.md](./monorepo.md) instead.

## 1) Stack

- Next.js (App Router; Next 14+).
- Tailwind CSS v4.
- shadcn/ui (Radix primitives under the hood).
- TypeScript strict mode.

## 2) Project Structure

- `app/` — server components by default; client components only when needed.
- `components/` — shared UI components.
- `features/` — feature modules (UI + logic + tests).
- `lib/` — server-side utilities (db, external APIs).
- `hooks/` — client-side hooks.
- `styles/` — Tailwind config and globals if needed.

## 3) Styling and Theming

- Tailwind v4:
  - Prefer `@theme` variables and CSS variables for design tokens.
  - Keep utility-first approach; avoid ad-hoc custom CSS unless necessary.
- shadcn/ui:
  - Install only the components you use.
  - Configure `components.json` once; align tokens with Tailwind theme.
- Dark mode via CSS variables; ensure accessible color contrast.

### Enforcement

- Map tokens to Tailwind v4 `@theme` and expose as CSS variables. Avoid arbitrary values; prefer token-backed utilities.
- Use Prettier Tailwind plugin to normalize class ordering.
- Use `eslint-plugin-tailwindcss` to catch invalid classes and encourage token usage.

## 4) Data Fetching and Caching

- Server Components by default for data-bound UI.
- Caching:
  - Use `fetch` options: `cache: 'force-cache'` for static, `cache: 'no-store'` for dynamic, or `next: { revalidate: N }` for ISR.
  - Invalidate with `revalidateTag`/`revalidatePath` where appropriate.
- Avoid long client-component waterfalls; lift data fetching to the server.

## 5) Performance

- Use Next/Image for responsive, optimized images.
- Prefetch links (`<Link prefetch />`) for critical routes.
- Split heavy client-only libraries with dynamic import.
- Monitor bundle size and avoid large dependencies without justification.

## 6) Testing

- Unit/integration:
  - Vitest + React Testing Library for components and hooks.
- e2e:
  - Playwright with a small set of high-value scenarios.
- Keep tests deterministic; mock network boundaries.

## 7) Security

- Enable strict security headers and CSP.
- Use `httpOnly`, `secure`, `sameSite` cookies for auth where applicable.
- Validate all user inputs server-side; never trust client input.
- Avoid leaking PII in logs/analytics.

## 8) Analytics and Web Vitals

- Use `useReportWebVitals` within a small client boundary to report LCP/FCP/CLS.
- Investigate regressions before release.

## 9) Cross-References

- General engineering standards: see [general.md](./general.md).
- Monorepo tooling/pipelines: see [monorepo.md](./monorepo.md).