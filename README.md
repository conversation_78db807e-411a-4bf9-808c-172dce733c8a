# Active Bite AI

A health and fitness application with an agentic AI coach that provides personalized meal and workout plans.

## Project Structure

This is a monorepo using Turborepo with the following structure:

- `apps/` - Contains the mobile app and admin panel
  - `mobile/` - Expo (React Native) mobile application
  - `admin/` - Next.js admin dashboard
- `packages/` - Shared packages
  - `ui/` - Shared UI components
  - `tokens/` - Design tokens
  - `database/` - Database client and types

## Technology Stack

- **Monorepo:** Turborepo with pnpm
- **Backend:** Supabase (Database, Auth, Storage, Edge Functions)
- **Mobile App:** Expo (React Native) for iOS & Android
- **Admin Panel:** Next.js
- **AI Engine:** Gemini 2.5 Flash
- **State Management:** Zustand (for client-side state)
- **Analytics:** PostHog (for user behavior tracking)
- **Payment Processing:** Stripe (for subscription management)
- **Push Notifications:** Expo Notifications
- **Error Monitoring:** Sentry
- **CI/CD:** GitHub Actions
- **Deployment:** Cloudflare Pages/Workers
- **Code Quality:** ESLint & Prettier
- **Testing:** Jest & React Testing Library

## Development

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Start development:
   ```bash
   pnpm dev
   ```

3. Run tests:
   ```bash
   pnpm test
   ```

## Deployment

- Mobile app: Expo Application Services (EAS)
- Admin panel: Cloudflare Pages
- API/Edge functions: Cloudflare Workers
