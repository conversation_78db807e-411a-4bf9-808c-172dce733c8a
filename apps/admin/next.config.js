/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  // Configuration for Cloudflare Pages
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true,
  },
  // Ensure compatibility with Cloudflare Workers
  webpack: (config) => {
    config.externals = [
      ...(config.externals || []),
      'sharp',
      'onnxruntime-node',
    ];
    return config;
  },
};

module.exports = nextConfig;
