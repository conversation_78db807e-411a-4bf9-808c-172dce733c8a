/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/ai-coach` | `/ai-coach`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/exercise` | `/exercise`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/nutrition` | `/nutrition`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/ai-persona`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/complete`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/general`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/goals`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/language`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/legal`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/nutrition`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/plan`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/units`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/welcome`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/workout`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/ai-coach` | `/ai-coach`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/exercise` | `/exercise`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/home` | `/home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/nutrition` | `/nutrition`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/ai-persona`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/complete`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/general`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/goals`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/language`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/legal`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/nutrition`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/plan`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/units`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/welcome`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/workout`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/login${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/ai-coach${`?${string}` | `#${string}` | ''}` | `/ai-coach${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/exercise${`?${string}` | `#${string}` | ''}` | `/exercise${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/home${`?${string}` | `#${string}` | ''}` | `/home${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/nutrition${`?${string}` | `#${string}` | ''}` | `/nutrition${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/onboarding/ai-persona${`?${string}` | `#${string}` | ''}` | `/onboarding/complete${`?${string}` | `#${string}` | ''}` | `/onboarding/general${`?${string}` | `#${string}` | ''}` | `/onboarding/goals${`?${string}` | `#${string}` | ''}` | `/onboarding/language${`?${string}` | `#${string}` | ''}` | `/onboarding/legal${`?${string}` | `#${string}` | ''}` | `/onboarding/nutrition${`?${string}` | `#${string}` | ''}` | `/onboarding/plan${`?${string}` | `#${string}` | ''}` | `/onboarding/units${`?${string}` | `#${string}` | ''}` | `/onboarding/welcome${`?${string}` | `#${string}` | ''}` | `/onboarding/workout${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/ai-coach` | `/ai-coach`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/exercise` | `/exercise`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/nutrition` | `/nutrition`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/ai-persona`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/complete`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/general`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/goals`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/language`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/legal`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/nutrition`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/plan`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/units`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/welcome`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/workout`; params?: Router.UnknownInputParams; };
    }
  }
}
