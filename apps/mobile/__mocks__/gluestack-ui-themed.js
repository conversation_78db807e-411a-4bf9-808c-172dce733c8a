const React = require('react')
const { Pressable, View, Text } = require('react-native')

const TextLike = ({ children, accessibilityLabel }) => React.createElement(Text, { accessibilityLabel }, children)
const ViewLike = ({ children }) => React.createElement(View, null, children)

const Button = ({ onPress, isDisabled, children }) => React.createElement(
  Pressable,
  { onPress: () => { if (!isDisabled && onPress) onPress() } },
  children
)

function extractText(node) {
  if (typeof node === 'string') return node
  if (Array.isArray(node)) {
    for (const n of node) {
      const t = extractText(n)
      if (t) return t
    }
  }
  if (node && node.props && node.props.children) {
    return extractText(node.props.children)
  }
  return null
}

const Checkbox = ({ isChecked, onChange, children }) => {
  const label = extractText(children) || undefined
  return React.createElement(
    Pressable,
    { accessibilityLabel: label, onPress: () => { if (onChange) onChange(!isChecked) } },
    children
  )
}

const Switch = ({ value, onValueChange }) => React.createElement(
  Pressable,
  { accessibilityLabel: 'switch', onPress: () => onValueChange && onValueChange(!value) },
  null
)

const RadioGroup = ({ _value, onChange, children }) => {
  const enhanced = React.Children.map(children, (child) => {
    if (!React.isValidElement(child)) return child
    const childValue = child.props.value
    return React.cloneElement(child, {
      onSelect: () => onChange && onChange(childValue)
    })
  })
  return React.createElement(View, null, enhanced)
}

const Radio = ({ onSelect, children }) => React.createElement(
  Pressable,
  { onPress: () => { if (onSelect) onSelect() } },
  children
)

const factory = new Proxy({}, {
  get: (_target, prop) => {
    if (prop === 'Button') return Button
    if (prop === 'ButtonText') return TextLike
    if (prop === 'Text') return TextLike
    if (prop === 'Heading') return TextLike
    if (prop === 'RadioLabel') return TextLike
    if (prop === 'RadioGroup') return RadioGroup
    if (prop === 'Radio') return Radio
    if (prop === 'Checkbox') return Checkbox
    if (prop === 'CheckboxLabel') return TextLike
    if (prop === 'Switch') return Switch
    // Others just containers
    if (['Center', 'VStack', 'HStack', 'CheckboxIndicator', 'CheckboxIcon', 'RadioIndicator', 'RadioIcon', 'CircleIcon'].includes(prop)) return ViewLike
    // Icons: render nothing
    if (prop === 'Icon') return () => null
    // Default to a simple container
    return ViewLike
  }
})

module.exports = factory

