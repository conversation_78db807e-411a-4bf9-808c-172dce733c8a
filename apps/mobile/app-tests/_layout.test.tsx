import React from 'react'
import { render } from '@testing-library/react-native'
import TabLayout from '../app/(tabs)/_layout'

// Mock expo-router Redirect to show href
jest.mock('expo-router', () => {
  const Tabs: any = ({ children }: any) => <>{children}</>
  ;(Tabs as any).Screen = ({ children }: any) => <>{children}</>
  return {
    Tabs,
    Redirect: ({ href }: any) => <>{href}</>,
  }
})

const mockUseAuth = jest.fn()
jest.mock('../src/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}))
const useAuthMock = mockUseAuth

// Icons imported via @gluestack-ui/themed may require setup; fallback to simple mocks
jest.mock('@gluestack-ui/themed', () => ({ Icon: ({ children }: any) => <>{children}</> }))

// Lucide icons - render nothing
jest.mock('lucide-react-native', () => ({ Home: () => null, MessageCircle: () => null, Utensils: () => null, Dumbbell: () => null, User: () => null }))

// tokens import
jest.mock('@active-bite-ai/tokens', () => ({ colors: { primary: '#000', textSecondary: '#111', surface: '#fff', border: '#eee' } }))

describe('Tabs auth guard', () => {
  it('redirects to login when unauthenticated', () => {
    useAuthMock.mockReturnValue({ currentUser: null, needsOnboarding: false })
    const { toJSON } = render(<TabLayout />)
    expect(toJSON()).toMatchInlineSnapshot(`"/(auth)/login"`)
  })

  it('redirects to onboarding when needs onboarding', () => {
    useAuthMock.mockReturnValue({ currentUser: { id: 'u1' }, needsOnboarding: true })
    const { toJSON } = render(<TabLayout />)
    expect(toJSON()).toMatchInlineSnapshot(`"/onboarding/welcome"`)
  })

  it('renders tabs when authed and no onboarding needed', () => {
    useAuthMock.mockReturnValue({ currentUser: { id: 'u1' }, needsOnboarding: false })
    const { toJSON } = render(<TabLayout />)
    // Ensure it does not redirect
    expect(toJSON()).not.toEqual('/(auth)/login')
    expect(toJSON()).not.toEqual('/onboarding/welcome')
  })
})

