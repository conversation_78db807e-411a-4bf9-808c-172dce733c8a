import React from 'react'
import { render } from '@testing-library/react-native'
import Index from '../app/index'

// Mock useAuth values per test
const mockUseAuth = jest.fn()
jest.mock('../src/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}))
const useAuthMock = mockUseAuth

// Mock expo-router Redirect component to capture href
jest.mock('expo-router', () => ({
  Redirect: ({ href }: any) => <>{href}</>,
}))

describe('Index redirects', () => {
  it('redirects to login when unauthenticated', () => {
    useAuthMock.mockReturnValue({ currentUser: null, needsOnboarding: false })
    const { toJSON } = render(<Index />)
    expect(toJSON()).toMatchInlineSnapshot(`"/(auth)/login"`)
  })

  it('redirects to onboarding when needsOnboarding', () => {
    useAuthMock.mockReturnValue({ currentUser: { id: 'u1' }, needsOnboarding: true })
    const { toJSON } = render(<Index />)
    expect(toJSON()).toMatchInlineSnapshot(`"/onboarding/welcome"`)
  })

  it('redirects to tabs when authed and no onboarding needed', () => {
    useAuthMock.mockReturnValue({ currentUser: { id: 'u1' }, needsOnboarding: false })
    const { toJSON } = render(<Index />)
    expect(toJSON()).toMatchInlineSnapshot(`"/(tabs)/home"`)
  })
})

