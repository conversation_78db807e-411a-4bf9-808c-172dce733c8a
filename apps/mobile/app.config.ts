import type { ExpoConfig } from 'expo/config'

const config: ExpoConfig = {
  name: 'Active Bite AI',
  slug: 'active-bite-ai',
  version: '1.0.0',
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'light',
  runtimeVersion: { policy: 'appVersion' },
  updates: {
    url: process.env.EAS_UPDATE_URL || 'https://u.expo.dev/your-project-id',
  },
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.activebiteai.app',
  },
  android: {
    adaptiveIcon: {
      backgroundColor: '#ffffff',
    },
    package: 'com.activebiteai.app',
  },
  web: {},
  extra: {
    eas: {
      projectId: process.env.EAS_PROJECT_ID || '',
    },
    EXPO_PUBLIC_SUPABASE_URL: process.env.EXPO_PUBLIC_SUPABASE_URL,
    EXPO_PUBLIC_SUPABASE_ANON_KEY: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
  },
  plugins: ['expo-router'],
  experiments: {
    typedRoutes: true,
  },
}

export default config

