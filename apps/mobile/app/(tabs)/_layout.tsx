import React from 'react';
import { Tabs, Redirect } from 'expo-router';
import { Icon } from '@gluestack-ui/themed';
import { Home, MessageCircle, Utensils, Dumbbell, User } from 'lucide-react-native';

import { colors as tokens } from '@active-bite-ai/tokens';
import { useAuth } from '../../src/contexts/AuthContext';

export default function TabLayout() {
  const { currentUser, needsOnboarding } = useAuth();

  if (!currentUser) {
    return <Redirect href="/(auth)/login" />;
  }
  if (needsOnboarding) {
    return <Redirect href="/onboarding/welcome" />;
  }

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: tokens.primary,
        tabBarInactiveTintColor: tokens.textSecondary,
        tabBarStyle: {
          backgroundColor: tokens.surface,
          borderTopWidth: 1,
          borderTopColor: tokens.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarItemStyle: {
          paddingBottom: 5,
          paddingTop: 5,
        },
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => (
            <Icon as={Home} size="sm" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="ai-coach"
        options={{
          title: 'AI Coach',
          tabBarIcon: ({ color }) => (
            <Icon as={MessageCircle} size="sm" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="nutrition"
        options={{
          title: 'Nutrition',
          tabBarIcon: ({ color }) => (
            <Icon as={Utensils} size="sm" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="exercise"
        options={{
          title: 'Exercise',
          tabBarIcon: ({ color }) => (
            <Icon as={Dumbbell} size="sm" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => (
            <Icon as={User} size="sm" color={color} />
          ),
        }}
      />
    </Tabs>
  );
}


