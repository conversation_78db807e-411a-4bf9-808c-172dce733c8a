import React from 'react';
import ExerciseScreen from '../../src/screens/ExerciseScreen';
import { useAuth } from '../../src/contexts/AuthContext';
import { useRouter } from 'expo-router';
import { useEffect } from 'react';

export default function ExerciseTab() {
  const { currentUser } = useAuth();
  const router = useRouter();
  
  useEffect(() => {
    if (!currentUser) {
      // Redirect to auth prompt or login
      router.push('/');
    }
  }, [currentUser, router]);
  
  if (!currentUser) {
    return null; // or a loading spinner
  }
  
  return <ExerciseScreen />;
}
