import React from 'react';
import { Stack } from 'expo-router';
import { AuthProvider } from '../src/contexts/AuthContext';
import { GluestackUIProvider } from '@gluestack-ui/themed';
import config from '../src/theme/gluestack.config';

export default function RootLayout() {
  return (
    <AuthProvider>
      <GluestackUIProvider config={config}>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="(auth)" />
          <Stack.Screen name="(tabs)" />
          <Stack.Screen name="onboarding" />
        </Stack>
      </GluestackUIProvider>
    </AuthProvider>
  );
}
