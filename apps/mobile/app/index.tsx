import React from 'react'
import { Redirect } from 'expo-router'
import { useAuth } from '../src/contexts/AuthContext'

export default function Index() {
  const { currentUser, needsOnboarding } = useAuth()

  if (!currentUser) {
    return <Redirect href="/(auth)/login" />
  }

  if (needsOnboarding) {
    return <Redirect href="/onboarding/welcome" />
  }

  return <Redirect href="/(tabs)/home" />
}
// With Expo Router, the tab navigator is automatically handled
// by the (tabs) directory structure
