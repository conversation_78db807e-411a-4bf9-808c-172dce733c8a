import React from 'react';
import { Stack } from 'expo-router';

export default function OnboardingLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="welcome" />
      <Stack.Screen name="language" />
      <Stack.Screen name="legal" />
      <Stack.Screen name="units" />
      <Stack.Screen name="general" />
      <Stack.Screen name="goals" />
      <Stack.Screen name="plan" />
      <Stack.Screen name="workout" />
      <Stack.Screen name="nutrition" />
      <Stack.Screen name="ai-persona" />
      <Stack.Screen name="complete" />
    </Stack>
  );
}
