module.exports = {
  preset: 'jest-expo',
  passWithNoTests: true,
  transformIgnorePatterns: [
    // Transform react-native, @react-native (incl. js-polyfills), expo, and related libs under pnpm's nested node_modules
    // Support pnpm path: node_modules/.pnpm/<pkg>@<ver>/node_modules/<scoped-or-name>/...
    'node_modules/(?!(?:\.pnpm/[^/]+/node_modules/)?((jest-)?react-native|react-native|@react-native$|@react-native(?:-community)?/.*|@react-native/js-polyfills|@react-native/.*|react-native-.*|@react-navigation/.*|expo(nent)?|@expo(nent)?/.*|expo-modules-core))'
  ],
  moduleNameMapper: {
    '^@react-native/js-polyfills(?:/.*)?$': '<rootDir>/__mocks__/polyfill-stub.js',
    '^@gluestack-ui/themed$': '<rootDir>/__mocks__/gluestack-ui-themed.js',
  },
  setupFilesAfterEnv: [
    '<rootDir>/jest.setup.js',
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
  },
};
