{"name": "@active-bite-ai/mobile", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "typecheck": "tsc --noEmit"}, "dependencies": {"@active-bite-ai/database": "workspace:*", "@active-bite-ai/tokens": "workspace:*", "@babel/runtime": "^7.28.3", "@gluestack-style/react": "^1.0.57", "@gluestack-ui/config": "^1.1.20", "@gluestack-ui/themed": "^1.1.73", "@react-native-async-storage/async-storage": "^2.2.0", "@supabase/supabase-js": "^2.45.1", "expo": "~52.0.14", "expo-constants": "~17.0.8", "expo-linking": "~7.0.5", "expo-router": "~4.0.21", "expo-status-bar": "~2.0.1", "lucide-react-native": "^0.539.0", "react": "18.3.1", "react-native": "0.76.9", "react-native-safe-area-context": "4.12.0", "react-native-screens": "4.4.0", "react-native-worklets": "^0.4.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.0.0", "@types/jest": "^29.0.0", "@types/react": "~18.3.23", "jest": "^29.0.0", "jest-expo": "~52.0.0", "typescript": "~5.3.3"}, "private": true}