import React from 'react';
import { Modal, StyleSheet, View } from 'react-native';
import { Button, Heading, Text, Center, VStack, ButtonText } from '@gluestack-ui/themed';
import { colors as tokens, spacing, borderRadius } from '@active-bite-ai/tokens';
import { useRouter } from 'expo-router';
import { useAuth } from '../contexts/AuthContext';

const hexToRgba = (hex: string, alpha: number): string => {
  const sanitized = hex.replace('#', '');
  const bigint = parseInt(sanitized, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

const OVERLAY_BG = hexToRgba(tokens.text, 0.5);
const CARD_BG = tokens.surface;

const AuthPromptModal = () => {
  const router = useRouter();
  const { currentUser } = useAuth();
  
  const handleSignIn = () => {
    router.push('/(auth)/login');
  };
  
  const handleClose = () => {
    // For now, we'll just close the modal by navigating away
    router.push('/(tabs)/home');
  };
  
  // If user is authenticated, don't show the modal
  if (currentUser) {
    return null;
  };
  
  return (
    <Modal 
      visible={true}
      transparent 
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.card}>
          <Center>
            <VStack space="md" alignItems="center">
              <Heading size="md" mb="$4">
                Unlock Full Benefits
              </Heading>
              <Text mb="$6" textAlign="center">
                Create an account to save your progress and get personalized AI plans.
              </Text>
              <Button 
                action="primary" 
                onPress={handleSignIn}
                w="$full"
                mb="$2"
              >
                <ButtonText>Sign In / Sign Up</ButtonText>
              </Button>
              <Button 
                variant="outline"
                onPress={handleClose}
                w="$full"
              >
                <ButtonText>Maybe Later</ButtonText>
              </Button>
            </VStack>
          </Center>
        </View>
      </View>
    </Modal>
  );
};

export default AuthPromptModal;

const styles = StyleSheet.create({
  card: {
    backgroundColor: CARD_BG,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    width: '80%',
  },
  overlay: {
    alignItems: 'center',
    backgroundColor: OVERLAY_BG,
    flex: 1,
    justifyContent: 'center',
  },
});
