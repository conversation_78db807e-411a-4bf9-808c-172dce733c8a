import React from 'react'
import { Text } from 'react-native'
import { render, waitFor } from '@testing-library/react-native'
import { AuthProvider, useAuth } from './AuthContext'

jest.mock('../utils/dataTransfer', () => ({
  transferOnboardingData: jest.fn().mockResolvedValue(true),
}))

// Mock database package with controllable fns
jest.mock('@active-bite-ai/database', () => ({
  getCurrentUser: jest.fn(),
  onAuthStateChange: jest.fn(),
  getProfile: jest.fn(),
  signUp: jest.fn(),
  signIn: jest.fn(),
  signOut: jest.fn(),
}))
const { getCurrentUser, onAuthStateChange, getProfile } = require('@active-bite-ai/database')


function Consumer() {
  const { currentUser, needsOnboarding } = useAuth()
  return (
    <>
      <Text testID="user">{currentUser ? 'yes' : 'no'}</Text>
      <Text testID="needs">{needsOnboarding ? 'yes' : 'no'}</Text>
    </>
  )
}

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('sets needsOnboarding=true when profile missing fields', async () => {
    getCurrentUser.mockResolvedValue({ user: { id: 'u1' }, error: null })
    getProfile.mockResolvedValue({ data: { id: 'u1', language: null, units: 'metric', terms_accepted_at: null }, error: null })
    onAuthStateChange.mockImplementation(() => ({ data: { subscription: { unsubscribe: jest.fn() } } }))

    const { getByTestId } = render(
      <AuthProvider>
        <Consumer />
      </AuthProvider>
    )

    // Ensure the hook fetched the user as a precondition
    await waitFor(() => expect(getCurrentUser).toHaveBeenCalled(), { timeout: 10000 })
    await waitFor(() => expect(getByTestId('user').props.children).toBe('yes'), { timeout: 10000 })
    await waitFor(() => expect(getByTestId('needs').props.children).toBe('yes'), { timeout: 10000 })
  })

  it('sets needsOnboarding=false when profile has required fields', async () => {
    getCurrentUser.mockResolvedValue({ user: { id: 'u2' }, error: null })
    getProfile.mockResolvedValue({ data: { id: 'u2', language: 'english', units: 'metric', terms_accepted_at: new Date().toISOString() }, error: null })
    onAuthStateChange.mockImplementation(() => ({ data: { subscription: { unsubscribe: jest.fn() } } }))

    const { getByTestId } = render(
      <AuthProvider>
        <Consumer />
      </AuthProvider>
    )

    await waitFor(() => expect(getByTestId('needs').props.children).toBe('no'), { timeout: 10000 })
  })

  it('calls transferOnboardingData on auth state change to logged in', async () => {
    const { transferOnboardingData } = require('../utils/dataTransfer')
    getCurrentUser.mockResolvedValue({ user: null, error: null })

    let cb: any
    onAuthStateChange.mockImplementation((callback: any) => {
      cb = callback
      return { data: { subscription: { unsubscribe: jest.fn() } } }
    })

    getProfile.mockResolvedValue({ data: { id: 'u3', language: null, units: 'metric', terms_accepted_at: null }, error: null })

    render(
      <AuthProvider>
        <Consumer />
      </AuthProvider>
    )

    // simulate login
    cb('SIGNED_IN', { user: { id: 'u3' } })

    await waitFor(() => expect(transferOnboardingData).toHaveBeenCalledWith('u3'), { timeout: 10000 })
  })
})

