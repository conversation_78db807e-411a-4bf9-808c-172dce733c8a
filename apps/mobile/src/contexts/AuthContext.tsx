import React, { createContext, useState, useEffect, useContext } from 'react';
import { User } from '@supabase/supabase-js';
import { signUp as authSignUp, signIn as authSignIn, signOut as authSignOut, getCurrentUser, onAuthStateChange, getProfile } from '@active-bite-ai/database';
import { transferOnboardingData } from '../utils/dataTransfer';

interface AuthContextType {
  currentUser: User | null;
  signUp: (email: string, password: string) => Promise<{ user: User | null; error: Error | null }>;
  signIn: (email: string, password: string) => Promise<{ user: User | null; error: Error | null }>;
  signOut: () => Promise<{ error: Error | null }>;
  needsOnboarding: boolean;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [needsOnboarding, setNeedsOnboarding] = useState(false)

  useEffect(() => {
    const fetchUser = async () => {
      const { user, error } = await getCurrentUser()
      if (error) {
        console.error('Error fetching user:', error)
      }
      setCurrentUser(user || null)
      if (user) await refreshProfileInternal(user.id)
    }

    fetchUser()

    const { data } = onAuthStateChange(async (_event, session) => {
      const newUser = session?.user || null
      setCurrentUser(newUser)
      if (newUser) {
        await refreshProfileInternal(newUser.id)
        // Transfer any local onboarding data
        await transferOnboardingData(newUser.id)
      } else {
        setNeedsOnboarding(false)
      }
    })

    return () => {
      data.subscription.unsubscribe()
    }
  }, [])

  const refreshProfileInternal = async (uid: string) => {
    try {
      const { data } = await getProfile(uid)
      // If no profile or missing required fields, require onboarding
      const needs = !data || !data.language || !data.units || !data.terms_accepted_at
      setNeedsOnboarding(needs)
    } catch {
      // On error, be safe and require onboarding
      setNeedsOnboarding(true)
    }
  }

  const signUp = async (email: string, password: string) => {
    const { data, error } = await authSignUp(email, password)
    return { user: data?.user || null, error: error || null }
  }

  const signIn = async (email: string, password: string) => {
    const { data, error } = await authSignIn(email, password)
    return { user: data?.user || null, error: error || null }
  }

  const signOut = async () => {
    const { error } = await authSignOut()
    setNeedsOnboarding(false);
    return { error: error || null }
  }

  const value = {
    currentUser,
    signUp,
    signIn,
    signOut,
    needsOnboarding,
    refreshProfile: async () => {
      if (currentUser?.id) await refreshProfileInternal(currentUser.id)
    }
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
