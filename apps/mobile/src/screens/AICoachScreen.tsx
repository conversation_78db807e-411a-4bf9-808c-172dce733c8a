import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TextInput } from 'react-native';
import { 
  Text, 
  Heading, 
  VStack, 
  HStack, 
  Box, 
  Button, 
  ButtonText, 
  Card
} from '@gluestack-ui/themed';
import { colors as tokens } from '@active-bite-ai/tokens';

export default function AICoachScreen() {
  const [messages, setMessages] = useState([
    {
      id: '1',
      text: "Hello! I'm your AI Coach. How can I help you today?",
      sender: 'ai',
      timestamp: new Date(Date.now() - 3600000)
    }
  ]);
  
  const [inputText, setInputText] = useState('');
  
  const handleSend = () => {
    if (inputText.trim() === '') return;
    
    // Add user message
    const newUserMessage = {
      id: Date.now().toString(),
      text: inputText,
      sender: 'user',
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, newUserMessage]);
    setInputText('');
    
    // Simulate AI response after a short delay
    setTimeout(() => {
      const aiResponse = {
        id: (Date.now() + 1).toString(),
        text: "Thanks for your message. As your AI coach, I'm here to help you with your fitness journey. What specific questions do you have today?",
        sender: 'ai',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };
  
  return (
    <View style={styles.container}>
      <Card size="md" variant="elevated" flex={1} m="$4" p="$4">
        <Heading size="md" mb="$4">Your AI Coach</Heading>
        <ScrollView style={styles.chatContainer}>
          <VStack space="md">
            {messages.map((message) => (
              <Box 
                key={message.id} 
                alignSelf={message.sender === 'user' ? 'flex-end' : 'flex-start'}
                maxWidth="$80"
              >
                <Card 
                  size="sm" 
                  variant="elevated"
                  bg={message.sender === 'user' ? '$primary500' : '$background50'}
                  borderColor={message.sender === 'user' ? '$primary500' : '$background300'}
                  borderWidth="$1"
                  p="$3"
                >
                  <Text
                    color={message.sender === 'user' ? '$white' : '$text800'}
                    fontWeight={message.sender === 'user' ? '$bold' : '$normal'}
                  >
                    {message.text}
                  </Text>
                </Card>
                <Text 
                  fontSize="$xs" 
                  color="$text500" 
                  mt="$1" 
                  alignSelf={message.sender === 'user' ? 'flex-end' : 'flex-start'}
                >
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </Box>
            ))}
          </VStack>
        </ScrollView>
        
        <HStack space="md">
          <TextInput
            style={styles.input}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Ask your AI coach..."
            multiline
          />
          <Button onPress={handleSend}>
            <ButtonText>Send</ButtonText>
          </Button>
        </HStack>
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  chatContainer: {
    flex: 1,
  },
  container: {
    backgroundColor: tokens.background,
    flex: 1,
  },
  input: {
    backgroundColor: tokens.surface,
    borderColor: tokens.border,
    borderRadius: 8,
    borderWidth: 1,
    flex: 1,
    padding: 12,
  },
});
