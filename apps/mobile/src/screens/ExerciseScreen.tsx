import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Text,
  Heading,
  VStack,
  HStack,
  Box,
  Button,
  ButtonText,
  Card,
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon
} from '@gluestack-ui/themed';
import { Check } from 'lucide-react-native';
import { colors as tokens } from '@active-bite-ai/tokens';

export default function ExerciseScreen() {
  // Mock data for workout plan
  const [workoutPlan] = useState({
    name: 'Upper Body Strength',
    duration: '45 min',
    exercises: [
      {
        id: '1',
        name: 'Push-ups',
        sets: 3,
        reps: 12,
        completed: true,
        weight: null
      },
      {
        id: '2',
        name: 'Bench Press',
        sets: 4,
        reps: 10,
        completed: false,
        weight: '135 lbs'
      },
      {
        id: '3',
        name: 'Dumbbell Rows',
        sets: 3,
        reps: 12,
        completed: false,
        weight: '50 lbs'
      },
      {
        id: '4',
        name: 'Overhead Press',
        sets: 3,
        reps: 10,
        completed: false,
        weight: '95 lbs'
      },
      {
        id: '5',
        name: '<PERSON><PERSON><PERSON> Curls',
        sets: 3,
        reps: 15,
        completed: false,
        weight: '25 lbs'
      }
    ]
  });
  
  const [completedExercises, setCompletedExercises] = useState(
    workoutPlan.exercises
      .filter(ex => ex.completed)
      .map(ex => ex.id)
  );
  
  const toggleExercise = (exerciseId: string) => {
    setCompletedExercises(prev => 
      prev.includes(exerciseId)
        ? prev.filter(id => id !== exerciseId)
        : [...prev, exerciseId]
    );
  };
  
  const progress = Math.round((completedExercises.length / workoutPlan.exercises.length) * 100);
  
  return (
    <View style={styles.container}>
      <ScrollView>
        <VStack space="md" p="$4">
          {/* Workout Header */}
          <Card size="md" variant="elevated" p="$4">
            <VStack space="xs" mb="$4">
              <Heading size="md">{workoutPlan.name}</Heading>
              <HStack space="md">
                <Text size="sm" color="$text500">{workoutPlan.duration}</Text>
                <Text size="sm" color="$text500">•</Text>
                <Text size="sm" color="$text500">{workoutPlan.exercises.length} exercises</Text>
              </HStack>
            </VStack>
            <HStack space="md" alignItems="center">
              <Text size="sm">Progress: {progress}%</Text>
              <Box flex={1} h="$2" bg="$background300" borderRadius="$full">
                <Box 
                  h="$2" 
                  bg="$primary500" 
                  borderRadius="$full" 
                  w={`${progress}%`} 
                />
              </Box>
            </HStack>
          </Card>
          
          {/* Exercises */}
          <Card size="md" variant="elevated" p="$4">
            <Heading size="md" mb="$4">Exercises</Heading>
            <VStack space="md">
              {workoutPlan.exercises.map((exercise) => (
                <Card 
                  key={exercise.id} 
                  size="sm" 
                  variant="elevated" 
                  bg={completedExercises.includes(exercise.id) ? '$success50' : '$background50'}
                  p="$3"
                >
                  <HStack alignItems="center" space="md">
                    <Checkbox 
                      size="md" 
                      isChecked={completedExercises.includes(exercise.id)}
                      onChange={() => toggleExercise(exercise.id)}
                      value={exercise.id}
                    >
                      <CheckboxIndicator>
                        <CheckboxIcon as={Check} />
                      </CheckboxIndicator>
                    </Checkbox>
                    <VStack flex={1}>
                      <Heading size="sm">{exercise.name}</Heading>
                      <HStack space="md">
                        <Text size="sm" color="$text500">{exercise.sets} sets</Text>
                        <Text size="sm" color="$text500">•</Text>
                        <Text size="sm" color="$text500">{exercise.reps} reps</Text>
                        {exercise.weight && (
                          <>
                            <Text size="sm" color="$text500">•</Text>
                            <Text size="sm" color="$text500">{exercise.weight}</Text>
                          </>
                        )}
                      </HStack>
                    </VStack>
                  </HStack>
                </Card>
              ))}
            </VStack>
            <HStack space="md" w="$full" mt="$4">
              <Button flex={1} variant="outline" action="secondary">
                <ButtonText>Skip</ButtonText>
              </Button>
              <Button flex={1} action="primary">
                <ButtonText>Finish Workout</ButtonText>
              </Button>
            </HStack>
          </Card>
          
          {/* Quick Actions */}
          <Card size="md" variant="elevated" p="$4">
            <Heading size="md" mb="$4">Quick Actions</Heading>
            <HStack space="md" justifyContent="space-between">
              <Button size="sm" flex={1} mr="$2" action="secondary">
                <ButtonText>Warm-up</ButtonText>
              </Button>
              <Button size="sm" flex={1} mr="$2">
                <ButtonText>Timer</ButtonText>
              </Button>
              <Button size="sm" flex={1} action="primary">
                <ButtonText>Log</ButtonText>
              </Button>
            </HStack>
          </Card>
        </VStack>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.background,
    flex: 1,
  },
});
