import React from 'react';
import { View, StyleSheet } from 'react-native';
import { 
  Text, 
  Heading, 
  VStack, 
  HStack, 
  Center, 
  Box, 
  Button, 
  ButtonText, 
  Card
} from '@gluestack-ui/themed';
import { useAuth } from '../contexts/AuthContext';
import { colors as tokens } from '@active-bite-ai/tokens';

export default function HomeScreen() {
  const { currentUser } = useAuth();
  
  // Mock data for demonstration
  const userData = {
    name: currentUser?.email?.split('@')[0] || 'User',
    todayWorkout: {
      name: 'Upper Body Strength',
      duration: '45 min',
      exercises: 8,
      completed: false
    },
    todayNutrition: {
      meals: [
        { name: 'Breakfast', eaten: true },
        { name: 'Lunch', eaten: false },
        { name: 'Dinner', eaten: false },
        { name: 'Snack', eaten: false }
      ]
    },
    weeklyConsistency: {
      days: [true, true, false, true, false, false, false],
      streak: 2
    }
  };

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="$full" p="$4">
          {/* Personalized Greeting */}
          <Heading size="2xl" mb="$2">
            Good morning, {userData.name}!
          </Heading>
          <Text size="md" color="$text500" mb="$6">
            Ready for another great day?
          </Text>
          
          {/* Today's Workout Card */}
          <Card size="md" variant="elevated" mb="$4" p="$4">
            <HStack alignItems="center" space="md" mb="$3">
              <Box w="$8" h="$8" borderRadius="$full" bg="$primary500" alignItems="center" justifyContent="center">
                <Text color="$white" fontWeight="$bold">W</Text>
              </Box>
              <VStack>
                <Heading size="md">Today’s Workout</Heading>
                <Text size="sm" color="$text500">
                  {userData.todayWorkout.duration} • {userData.todayWorkout.exercises} exercises
                </Text>
              </VStack>
            </HStack>
            <Heading size="lg" mb="$2">
              {userData.todayWorkout.name}
            </Heading>
            <Text size="sm" color="$text500" mb="$4">
              Focus on proper form and controlled movements
            </Text>
            <Button 
              action={userData.todayWorkout.completed ? "primary" : "primary"}
              w="$full"
            >
              <ButtonText>
                {userData.todayWorkout.completed ? 'Completed' : 'Start Workout'}
              </ButtonText>
            </Button>
          </Card>
          
          {/* Today's Nutrition Card */}
          <Card size="md" variant="elevated" mb="$4" p="$4">
            <HStack alignItems="center" space="md" mb="$3">
              <Box w="$8" h="$8" borderRadius="$full" bg="$secondary500" alignItems="center" justifyContent="center">
                <Text color="white" fontWeight="$bold">N</Text>
              </Box>
              <VStack>
                <Heading size="md">Today’s Nutrition</Heading>
                <Text size="sm" color="$text500">
                  Track your meals throughout the day
                </Text>
              </VStack>
            </HStack>
            <VStack space="sm">
              {userData.todayNutrition.meals.map((meal, index) => (
                <HStack key={index} justifyContent="space-between" alignItems="center">
                  <Text size="md">{meal.name}</Text>
                  <Button 
                    size="sm" 
                    variant={meal.eaten ? "solid" : "outline"}
                    action={meal.eaten ? "primary" : "primary"}
                  >
                    <ButtonText size="sm">
                      {meal.eaten ? 'Eaten' : 'Mark'}
                    </ButtonText>
                  </Button>
                </HStack>
              ))}
            </VStack>
          </Card>
          
          {/* AI Coach's Corner */}
          <Card size="md" variant="elevated" mb="$4" p="$4">
            <HStack alignItems="center" space="md" mb="$3">
              <Box w="$8" h="$8" borderRadius="$full" bg="$accent500" alignItems="center" justifyContent="center">
                <Text color="white" fontWeight="$bold">AI</Text>
              </Box>
              <VStack>
                <Heading size="md">AI Coach’s Corner</Heading>
                <Text size="sm" color="$text500">
                  Your daily tip
                </Text>
              </VStack>
            </HStack>
            <Text size="md" italic>
              “Remember to stay hydrated throughout your workout. Aim for at least 8oz of water every 15–20 minutes during exercise.”
            </Text>
          </Card>
          
          {/* Weekly Consistency Tracker */}
          <Card size="md" variant="elevated" p="$4">
            <VStack mb="$3">
              <Heading size="md">Weekly Consistency</Heading>
              <Text size="sm" color="$text500">
                {userData.weeklyConsistency.streak} day streak
              </Text>
            </VStack>
            <HStack space="sm" justifyContent="space-between">
              {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
                <VStack key={index} alignItems="center">
                  <Text size="sm" color="$text500">{day}</Text>
                  <Box 
                    w="$8" 
                    h="$8" 
                    borderRadius="$full" 
                    bg={userData.weeklyConsistency.days[index] ? "$primary500" : "$background300"}
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Text size="sm" color={userData.weeklyConsistency.days[index] ? "$white" : "$text500"}>
                      {index + 1}
                    </Text>
                  </Box>
                </VStack>
              ))}
            </HStack>
          </Card>
        </VStack>
      </Center>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.background,
    flex: 1,
  },
});
