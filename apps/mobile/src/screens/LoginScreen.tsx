import React, { useState } from 'react'
import { View, StyleSheet, Alert } from 'react-native'
import { Button, ButtonText, Heading, Input, InputField, InputIcon, InputSlot, FormControl, FormControlLabel, FormControlLabelText, VStack, Center } from '@gluestack-ui/themed'
import { Eye, EyeOff } from 'lucide-react-native'
import { useRouter } from 'expo-router'
import { useAuth } from '../contexts/AuthContext'
import { colors as tokens } from '@active-bite-ai/tokens'

export default function LoginScreen() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isSignUp, setIsSignUp] = useState(false)
  const { signUp, signIn } = useAuth()
  const router = useRouter()

  const handleAuth = async () => {
    const emailTrimmed = email.trim().toLowerCase()
    const passwordTrimmed = password
    if (!emailTrimmed || !passwordTrimmed) {
      Alert.alert('Error', 'Please fill in all fields')
      return
    }

    try {
      if (isSignUp) {
        const { error } = await signUp(emailTrimmed, passwordTrimmed)
        if (error) {
          Alert.alert('Sign Up Error', error.message || 'Unable to sign up')
        } else {
          Alert.alert('Success', 'Account created! Please check your email for confirmation.')
        }
      } else {
        const { error } = await signIn(emailTrimmed, passwordTrimmed)
        if (error) {
          Alert.alert('Sign In Error', error.message || 'Invalid email or password')
        } else {
          // Navigate to root; index.tsx will redirect to tabs or onboarding
          router.replace('/')
        }
      }
    } catch {
      Alert.alert('Error', 'An unexpected error occurred')
    }
  }

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%">
          <Heading size="xl" mb="$6">{isSignUp ? 'Create Account' : 'Welcome Back'}</Heading>
          
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Email</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </Input>
          </FormControl>
          
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Password</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                accessibilityLabel="Password"
                autoCapitalize="none"
              />
              <InputSlot
                pr="$3"
                onPress={() => setShowPassword((v) => !v)}
                accessibilityLabel={showPassword ? 'Hide password' : 'Show password'}
              >
                <InputIcon as={showPassword ? EyeOff : Eye} />
              </InputSlot>
            </Input>
          </FormControl>
          
          <Button
            action="primary"
            size="lg"
            onPress={handleAuth}
            mb="$4"
          >
            <ButtonText>{isSignUp ? 'Sign Up' : 'Sign In'}</ButtonText>
          </Button>
          
          <Button
            variant="link"
            onPress={() => setIsSignUp(!isSignUp)}
          >
            <ButtonText>
              {isSignUp ? 'Already have an account? Sign In' : 'Don’t have an account? Sign Up'}
            </ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.background,
    flex: 1,
  },
})
