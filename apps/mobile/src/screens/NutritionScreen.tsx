import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  ButtonText,
  Card,
  Progress,
  ProgressFilledTrack
} from '@gluestack-ui/themed';
import { colors as tokens } from '@active-bite-ai/tokens';

export default function NutritionScreen() {
  // Mock data for nutrition tracking
  const [dailyGoals] = useState({
    calories: 2000,
    protein: 150,
    carbs: 250,
    fat: 70
  });
  
  const [consumed] = useState({
    calories: 1250,
    protein: 95,
    carbs: 180,
    fat: 45
  });
  
  const [meals] = useState([
    {
      id: '1',
      name: 'Breakfast',
      time: '8:30 AM',
      items: [
        { name: 'Oatmeal with berries', calories: 320 },
        { name: 'Greek yogurt', calories: 120 }
      ],
      totalCalories: 440
    },
    {
      id: '2',
      name: 'Lunch',
      time: '12:15 PM',
      items: [
        { name: 'Grilled chicken salad', calories: 380 },
        { name: 'Apple', calories: 95 }
      ],
      totalCalories: 475
    },
    {
      id: '3',
      name: 'Dinner',
      time: '7:00 PM',
      items: [
        { name: 'Salmon with quinoa', calories: 450 },
        { name: 'Steamed vegetables', calories: 85 }
      ],
      totalCalories: 535
    }
  ]);
  
  const nutrientProgress = [
    { name: 'Calories', current: consumed.calories, goal: dailyGoals.calories, unit: 'kcal' },
    { name: 'Protein', current: consumed.protein, goal: dailyGoals.protein, unit: 'g' },
    { name: 'Carbs', current: consumed.carbs, goal: dailyGoals.carbs, unit: 'g' },
    { name: 'Fat', current: consumed.fat, goal: dailyGoals.fat, unit: 'g' }
  ];
  
  return (
    <View style={styles.container}>
      <ScrollView>
        <VStack space="md" p="$4">
          {/* Daily Nutrition Summary */}
          <Card size="md" variant="elevated" p="$4">
            <Heading size="md" mb="$4">Today’s Nutrition</Heading>
            <VStack space="md">
              {nutrientProgress.map((nutrient, index) => (
                <VStack key={index} space="xs">
                  <HStack justifyContent="space-between">
                    <Text size="md" fontWeight="$bold">{nutrient.name}</Text>
                    <Text size="md">{nutrient.current} / {nutrient.goal} {nutrient.unit}</Text>
                  </HStack>
                  <Progress value={(nutrient.current / nutrient.goal) * 100} size="sm">
                    <ProgressFilledTrack />
                  </Progress>
                </VStack>
              ))}
            </VStack>
          </Card>
          
          {/* Meals */}
          <Card size="md" variant="elevated" p="$4">
            <Heading size="md" mb="$4">Meals</Heading>
            <VStack space="md">
              {meals.map((meal) => (
                <Card key={meal.id} size="sm" variant="elevated" bg="$background50" p="$3" mb="$3">
                  <HStack justifyContent="space-between" w="$full" mb="$2">
                    <Heading size="sm">{meal.name}</Heading>
                    <Text size="sm" color="$text500">{meal.time}</Text>
                  </HStack>
                  <VStack space="xs" mb="$3">
                    {meal.items.map((item, idx) => (
                      <HStack key={idx} justifyContent="space-between">
                        <Text size="sm">{item.name}</Text>
                        <Text size="sm" color="$text500">{item.calories} kcal</Text>
                      </HStack>
                    ))}
                  </VStack>
                  <HStack justifyContent="space-between" w="$full" pt="$2" borderTopWidth="$1" borderColor="$background200">
                    <Text size="sm" fontWeight="$bold">Total</Text>
                    <Text size="sm" fontWeight="$bold">{meal.totalCalories} kcal</Text>
                  </HStack>
                </Card>
              ))}
            </VStack>
            <Button w="$full" action="primary" mt="$4">
              <ButtonText>Add Meal</ButtonText>
            </Button>
          </Card>
          
          {/* Quick Add */}
          <Card size="md" variant="elevated" p="$4">
            <Heading size="md" mb="$4">Quick Add</Heading>
            <HStack space="md" justifyContent="space-between">
              <Button size="sm" flex={1} mr="$2">
                <ButtonText>Water</ButtonText>
              </Button>
              <Button size="sm" flex={1} mr="$2" action="secondary">
                <ButtonText>Snack</ButtonText>
              </Button>
              <Button size="sm" flex={1} action="primary">
                <ButtonText>Recipe</ButtonText>
              </Button>
            </HStack>
          </Card>
        </VStack>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.background,
    flex: 1,
  },
});
