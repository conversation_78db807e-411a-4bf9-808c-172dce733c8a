import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Text,
  Heading,
  VStack,
  HStack,
  Box,
  Button,
  ButtonText,
  Card,
  Icon
} from '@gluestack-ui/themed';
import { User, Settings, Bell, HelpCircle, LogOut, Mail } from 'lucide-react-native';
import { useAuth } from '../contexts/AuthContext';
import { colors as tokens } from '@active-bite-ai/tokens';

export default function ProfileScreen() {
  const { currentUser, signOut } = useAuth();
  
  // Mock user data
  const userData = {
    name: currentUser?.email?.split('@')[0] || 'User',
    email: currentUser?.email || '<EMAIL>',
    memberSince: 'January 2024',
    workoutsCompleted: 24,
    nutritionLogs: 127,
    consistencyStreak: 7
  };
  
  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };
  
  return (
    <View style={styles.container}>
      <ScrollView>
        <VStack space="md" p="$4">
          {/* Profile Header */}
          <Card size="md" variant="elevated" p="$4">
            <HStack space="md" alignItems="center">
              <Box 
                w="$16" 
                h="$16" 
                borderRadius="$full" 
                bg="$primary500" 
                alignItems="center" 
                justifyContent="center"
              >
                <Icon as={User} size="xl" color="$textLight0" />
              </Box>
              <VStack flex={1}>
                <Heading size="lg">{userData.name}</Heading>
                <Text size="md" color="$text500">{userData.email}</Text>
                <Text size="sm" color="$text500">Member since {userData.memberSince}</Text>
              </VStack>
            </HStack>
          </Card>
          
          {/* Stats */}
          <Card size="md" variant="elevated" p="$4">
            <Heading size="md" mb="$4">Your Stats</Heading>
            <HStack space="md" justifyContent="space-between">
              <VStack alignItems="center">
                <Heading size="lg">{userData.workoutsCompleted}</Heading>
                <Text size="sm" color="$text500">Workouts</Text>
              </VStack>
              <VStack alignItems="center">
                <Heading size="lg">{userData.nutritionLogs}</Heading>
                <Text size="sm" color="$text500">Nutrition Logs</Text>
              </VStack>
              <VStack alignItems="center">
                <Heading size="lg">{userData.consistencyStreak}</Heading>
                <Text size="sm" color="$text500">Day Streak</Text>
              </VStack>
            </HStack>
          </Card>
          
          {/* Account Settings */}
          <Card size="md" variant="elevated" p="$4">
            <Heading size="md" mb="$4">Account Settings</Heading>
            <VStack space="md">
              <Button 
                variant="outline" 
                action="secondary" 
                justifyContent="flex-start"
                onPress={() => console.log('Edit Profile')}
              >
                <Icon as={User} size="sm" mr="$3" />
                <ButtonText>Edit Profile</ButtonText>
              </Button>
              <Button 
                variant="outline" 
                action="secondary" 
                justifyContent="flex-start"
                onPress={() => console.log('Notifications')}
              >
                <Icon as={Bell} size="sm" mr="$3" />
                <ButtonText>Notifications</ButtonText>
              </Button>
              <Button 
                variant="outline" 
                action="secondary" 
                justifyContent="flex-start"
                onPress={() => console.log('Preferences')}
              >
                <Icon as={Settings} size="sm" mr="$3" />
                <ButtonText>Preferences</ButtonText>
              </Button>
            </VStack>
          </Card>
          
          {/* Support */}
          <Card size="md" variant="elevated" p="$4">
            <Heading size="md" mb="$4">Support</Heading>
            <VStack space="md">
              <Button 
                variant="outline" 
                action="secondary" 
                justifyContent="flex-start"
                onPress={() => console.log('Help Center')}
              >
                <Icon as={HelpCircle} size="sm" mr="$3" />
                <ButtonText>Help Center</ButtonText>
              </Button>
              <Button 
                variant="outline" 
                action="secondary" 
                justifyContent="flex-start"
                onPress={() => console.log('Contact Us')}
              >
                <Icon as={Mail} size="sm" mr="$3" />
                <ButtonText>Contact Us</ButtonText>
              </Button>
            </VStack>
          </Card>
          
          {/* Sign Out */}
          <Card size="md" variant="elevated" p="$4">
            <Button 
              action="negative" 
              w="$full"
              onPress={handleSignOut}
            >
              <Icon as={LogOut} size="sm" mr="$3" />
              <ButtonText>Sign Out</ButtonText>
            </Button>
          </Card>
        </VStack>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.background,
    flex: 1,
  },
});
