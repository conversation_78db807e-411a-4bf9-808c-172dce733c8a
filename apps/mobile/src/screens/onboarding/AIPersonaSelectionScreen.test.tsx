import React from 'react'
import { render, fireEvent, waitFor } from '@testing-library/react-native'
import AIPersonaSelectionScreen from './AIPersonaSelectionScreen'

// Mock expo-router navigation
const mockPush = jest.fn()
jest.mock('expo-router', () => ({ useRouter: () => ({ push: mockPush }) }))

// Mock storage
jest.mock('../../utils/onboardingStorage', () => ({
  saveOnboardingData: jest.fn().mockResolvedValue(true),
}))
const { saveOnboardingData } = require('../../utils/onboardingStorage')
// Mock tokens to avoid undefined
jest.mock('@active-bite-ai/tokens', () => ({ colors: { surface: '#fff' } }))

describe('AIPersonaSelectionScreen', () => {
  beforeEach(() => { jest.clearAllMocks() })

  it('requires a selection and then navigates to complete', async () => {
    const { getByText } = render(<AIPersonaSelectionScreen />)

    // Finish Setup without selection should not call save
    fireEvent.press(getByText('Finish Setup'))
    expect(saveOnboardingData).not.toHaveBeenCalled()

    // Choose a persona
    fireEvent.press(getByText('Motivator'))
    fireEvent.press(getByText('Finish Setup'))

    await waitFor(() => expect(saveOnboardingData).toHaveBeenCalled())
    await waitFor(() => expect(mockPush).toHaveBeenCalledWith('/onboarding/complete'))
  })
})

