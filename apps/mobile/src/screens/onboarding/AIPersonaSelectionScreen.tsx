import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Button, ButtonText, Text, Heading, Center, VStack, Radio, RadioGroup, RadioIndicator, RadioIcon, RadioLabel, CircleIcon } from '@gluestack-ui/themed';
import { colors as tokens } from '@active-bite-ai/tokens';
import { useRouter } from 'expo-router';
import { saveOnboardingData } from '../../utils/onboardingStorage';

const AIPersonaSelectionScreen = () => {
  const router = useRouter();
  const [selectedPersona, setSelectedPersona] = useState('');

  const handleContinue = async () => {
    await saveOnboardingData({ aiPersona: selectedPersona });
    router.push('/onboarding/complete');
  };

  const personas = [
    { 
      id: 'motivator', 
      label: 'Motivator', 
      description: 'Encouraging and positive, celebrates your wins and helps you stay on track' 
    },
    { 
      id: 'drill-sergeant', 
      label: 'Drill Sergeant', 
      description: 'Strict and demanding, pushes you to your limits' 
    },
    { 
      id: 'nutritionist', 
      label: 'Nutritionist', 
      description: 'Knowledgeable about food and nutrition, focuses on healthy eating' 
    },
    { 
      id: 'coach', 
      label: 'Personal Coach', 
      description: 'Balanced approach, provides guidance and support' 
    },
  ];

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%">
          <Heading size="xl" mb="$6">Choose Your AI Coach</Heading>
          <Text size="md" mb="$6">
            Select the personality that will motivate you on your fitness journey
          </Text>
          
          <ScrollView style={styles.scrollView}>
            <RadioGroup 
              value={selectedPersona}
              onChange={(value: string) => setSelectedPersona(value)}
            >
              {personas.map((persona) => (
                <Radio key={persona.id} value={persona.id} size="md" mb="$6">
                  <RadioIndicator mr="$2">
                    <RadioIcon as={CircleIcon} />
                  </RadioIndicator>
                  <VStack>
                    <RadioLabel fontWeight="$bold">{persona.label}</RadioLabel>
                    <Text size="sm" color="$text500" mt="$1">{persona.description}</Text>
                  </VStack>
                </Radio>
              ))}
            </RadioGroup>
          </ScrollView>
          
          <Button
            action="primary"
            size="lg"
            onPress={handleContinue}
            isDisabled={!selectedPersona}
            mt="$6"
          >
            <ButtonText color="$white" fontWeight="$bold">Finish Setup</ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
  scrollView: {
    maxHeight: 400,
  },
});

export default AIPersonaSelectionScreen;
