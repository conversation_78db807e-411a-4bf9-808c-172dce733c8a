import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Text, Heading, Center, VStack, ButtonText } from '@gluestack-ui/themed';
import { colors as tokens } from '@active-bite-ai/tokens';
import { useRouter } from 'expo-router';

const AuthPromptScreen = () => {
  const router = useRouter();

  const handleSignIn = () => {
    router.push('/login');
  };

  const handleSignUp = () => {
    router.push('/login'); // For now, we'll use the same screen for both
  };

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%" alignItems="center">
          <Heading size="xl" mb="$6">Almost There!</Heading>
          <Text size="md" mb="$6" textAlign="center">
            Create an account or sign in to save your progress and get personalized recommendations
          </Text>
          
          <Button
            action="primary"
            size="lg"
            onPress={handleSignUp}
            w="$full"
            mb="$4"
          >
            <ButtonText>Create Account</ButtonText>
          </Button>
          
          <Button
            variant="outline"
            size="lg"
            onPress={handleSignIn}
            w="$full"
          >
            <ButtonText>I Already Have an Account</ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  );
};

export default AuthPromptScreen;

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
});
