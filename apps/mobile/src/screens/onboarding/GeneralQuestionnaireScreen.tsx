import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, ButtonText, Text, Heading, Center, VStack, FormControl, FormControlLabel, FormControlLabelText, Input, InputField } from '@gluestack-ui/themed';
import { colors as tokens } from '@active-bite-ai/tokens';
import { useRouter } from 'expo-router';
import { saveOnboardingData } from '../../utils/onboardingStorage';

const GeneralQuestionnaireScreen = () => {
  const router = useRouter();
  const [age, setAge] = useState('');
  const [height, setHeight] = useState('');
  const [weight, setWeight] = useState('');

  const handleContinue = async () => {
    // Basic validation
    if (!age || !height || !weight) {
      alert('Please fill in all fields');
      return;
    }
    
    if (isNaN(Number(age)) || isNaN(Number(height)) || isNaN(Number(weight))) {
      alert('Please enter valid numbers');
      return;
    }
    
    await saveOnboardingData({
      age: Number(age),
      height: Number(height),
      weight: Number(weight),
    });
    router.push('/onboarding/goals');
  };

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%">
          <Heading size="xl" mb="$10">General Information</Heading>
          <Text size="md" mb="$6">
            Help us create the perfect plan for you
          </Text>
          
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Age</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField 
                placeholder="Enter your age"
                value={age}
                onChangeText={setAge}
                keyboardType="numeric"
              />
            </Input>
          </FormControl>
          
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Height (cm)</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField 
                placeholder="Enter your height in cm"
                value={height}
                onChangeText={setHeight}
                keyboardType="numeric"
              />
            </Input>
          </FormControl>
          
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Weight (kg)</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField 
                placeholder="Enter your weight in kg"
                value={weight}
                onChangeText={setWeight}
                keyboardType="numeric"
              />
            </Input>
          </FormControl>
          
          <Button
            action="primary"
            size="lg"
            onPress={handleContinue}
            mt="$6"
          >
            <ButtonText color="$white" fontWeight="$bold">
              Continue
            </ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
});

export default GeneralQuestionnaireScreen;
