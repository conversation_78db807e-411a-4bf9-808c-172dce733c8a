import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, ButtonText, Text, Heading, Center, VStack, Checkbox, CheckboxGroup, CheckboxIndicator, CheckboxIcon, CheckboxLabel, CheckIcon } from '@gluestack-ui/themed';
import { useRouter } from 'expo-router';
import { saveOnboardingData } from '../../utils/onboardingStorage';
import { colors as tokens } from '@active-bite-ai/tokens';

const GoalsSelectionScreen = () => {
  const router = useRouter();
  const [selectedGoals, setSelectedGoals] = useState<string[]>([]);

  const handleContinue = async () => {
    await saveOnboardingData({ primaryGoals: selectedGoals });
    router.push('/onboarding/plan');
  };

  const goalsOptions = [
    { id: 'weight-loss', label: 'Weight Loss' },
    { id: 'muscle-gain', label: 'Muscle Gain' },
    { id: 'endurance', label: 'Improve Endurance' },
    { id: 'flexibility', label: 'Increase Flexibility' },
    { id: 'stress-relief', label: 'Stress Relief' },
    { id: 'general-fitness', label: 'General Fitness' },
  ];

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%">
          <Heading size="xl" mb="$10">What Are Your Goals?</Heading>
          <Text size="md" mb="$6">
            Select one or more fitness goals you’d like to focus on
          </Text>
          
          <CheckboxGroup 
            value={selectedGoals}
            onChange={setSelectedGoals}
          >
            {goalsOptions.map((goal) => (
              <Checkbox key={goal.id} value={goal.id} size="md" mb="$4">
                <CheckboxIndicator mr="$2">
                  <CheckboxIcon as={CheckIcon} />
                </CheckboxIndicator>
                <CheckboxLabel>{goal.label}</CheckboxLabel>
              </Checkbox>
            ))}
          </CheckboxGroup>
          
          <Button
            action="primary"
            size="lg"
            onPress={handleContinue}
            isDisabled={selectedGoals.length === 0}
            mt="$6"
          >
            <ButtonText color="$white" fontWeight="$bold">
              Continue
            </ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
});

export default GoalsSelectionScreen;
