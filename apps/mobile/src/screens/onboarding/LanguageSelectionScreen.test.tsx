import React from 'react'
import { render, fireEvent, waitFor } from '@testing-library/react-native'
import LanguageSelectionScreen from './LanguageSelectionScreen'

// Mock expo-router navigation
const mockPush = jest.fn()
jest.mock('expo-router', () => ({ useRouter: () => ({ push: mockPush }) }))

// Mock storage
jest.mock('../../utils/onboardingStorage', () => ({
  saveOnboardingData: jest.fn().mockResolvedValue(true),
}))
const { saveOnboardingData } = require('../../utils/onboardingStorage')

// Mock db updateProfile used in screen
jest.mock('@active-bite-ai/database', () => ({ updateProfile: jest.fn().mockResolvedValue({ data: {}, error: null }) }))
// Note: updateProfile is mocked but not directly asserted in this test
// Mock tokens to avoid undefined
jest.mock('@active-bite-ai/tokens', () => ({ colors: { surface: '#fff' } }))

// Mock auth context
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({ currentUser: { id: 'u1' }, refreshProfile: jest.fn() }),
}))

describe('LanguageSelectionScreen', () => {
  beforeEach(() => { jest.clearAllMocks() })

  it('saves language and navigates to legal', async () => {
    const { getByText } = render(<LanguageSelectionScreen />)

    fireEvent.press(getByText('Continue'))

    await waitFor(() => expect(saveOnboardingData).toHaveBeenCalledWith({ language: 'english' }))
    await waitFor(() => expect(mockPush).toHaveBeenCalledWith('/onboarding/legal'))
  })
})

