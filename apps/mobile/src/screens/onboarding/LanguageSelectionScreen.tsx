import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Text, Heading, Center, VStack, Radio, RadioGroup, RadioIndicator, RadioIcon, RadioLabel, CircleIcon, ButtonText } from '@gluestack-ui/themed';
import { useRouter } from 'expo-router';
import { saveOnboardingData } from '../../utils/onboardingStorage';
import { colors as tokens } from '@active-bite-ai/tokens';
import { updateProfile } from '@active-bite-ai/database';
import { useAuth } from '../../contexts/AuthContext';

const LanguageSelectionScreen = () => {
  const router = useRouter();
  const { currentUser, refreshProfile } = useAuth();
  const [selectedLanguage, setSelectedLanguage] = useState('english');
  const [submitting, setSubmitting] = useState(false);

  const handleContinue = async () => {
    if (submitting) return;
    setSubmitting(true);
    try {
      await saveOnboardingData({ language: selectedLanguage });
      if (currentUser?.id) {
        await updateProfile(currentUser.id, { language: selectedLanguage });
        await refreshProfile();
      }
      router.push('/onboarding/legal');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%">
          <Heading size="xl" mb="$10">Select Your Language</Heading>
          <Text size="md" mb="$6">
            Choose your preferred language for the app interface
          </Text>
          
          <RadioGroup 
            value={selectedLanguage}
            onChange={setSelectedLanguage}
          >
            <Radio value="english" size="md" mb="$4">
              <RadioIndicator mr="$2">
                <RadioIcon as={CircleIcon} />
              </RadioIndicator>
              <RadioLabel>English</RadioLabel>
            </Radio>
            <Radio value="spanish" size="md" mb="$4">
              <RadioIndicator mr="$2">
                <RadioIcon as={CircleIcon} />
              </RadioIndicator>
              <RadioLabel>Spanish</RadioLabel>
            </Radio>
            <Radio value="french" size="md" mb="$4">
              <RadioIndicator mr="$2">
                <RadioIcon as={CircleIcon} />
              </RadioIndicator>
              <RadioLabel>French</RadioLabel>
            </Radio>
            <Radio value="german" size="md" mb="$4">
              <RadioIndicator mr="$2">
                <RadioIcon as={CircleIcon} />
              </RadioIndicator>
              <RadioLabel>German</RadioLabel>
            </Radio>
          </RadioGroup>
          
          <Button
            action="primary"
            size="lg"
            onPress={handleContinue}
            style={styles.button}
            mt="$6"
            isDisabled={submitting}
          >
            <ButtonText color="$white" fontWeight="$bold">
              Continue
            </ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  );
};

const styles = StyleSheet.create({
  button: {
    width: '100%',
  },
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
});

export default LanguageSelectionScreen;
