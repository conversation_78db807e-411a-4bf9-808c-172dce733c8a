import React from 'react'
import { render, fireEvent, waitFor } from '@testing-library/react-native'
import LegalConsentScreen from './LegalConsentScreen'

// Mock expo-router navigation
const mockPush = jest.fn()
jest.mock('expo-router', () => ({ useRouter: () => ({ push: mockPush }) }))

// Mock db
jest.mock('@active-bite-ai/database', () => ({
  updateProfile: jest.fn().mockResolvedValue({ data: {}, error: null }),
}))
const { updateProfile } = require('@active-bite-ai/database')

// Mock tokens to avoid undefined
jest.mock('@active-bite-ai/tokens', () => ({ colors: { surface: '#fff' } }))

// Mock auth
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({ currentUser: { id: 'u1' }, refreshProfile: jest.fn() }),
}))

describe('LegalConsentScreen', () => {
  beforeEach(() => { jest.clearAllMocks() })

  it('requires all toggles then saves and navigates to units', async () => {
    const { getByText, getAllByLabelText } = render(<LegalConsentScreen />)

    // Check checkboxes
    const checkboxes = getAllByLabelText('I agree')
    fireEvent.press(checkboxes[0])
    fireEvent.press(checkboxes[1])

    // Acknowledge disclaimer (label is different)
    fireEvent.press(getByText('I acknowledge'))

    // Optional switch for marketing - not required

    // Continue
    fireEvent.press(getByText('Continue'))

    await waitFor(() => expect(updateProfile).toHaveBeenCalled())
    await waitFor(() => expect(mockPush).toHaveBeenCalledWith('/onboarding/units'))
  })
})

