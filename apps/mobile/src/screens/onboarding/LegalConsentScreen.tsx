import React, { useState } from 'react'
import { View, StyleSheet, Linking } from 'react-native'
import {
  Button,
  ButtonText,
  Text,
  Heading,
  Center,
  VStack,
  HStack,
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon,
  CheckboxLabel,
  Switch,
} from '@gluestack-ui/themed'
import { CheckIcon } from 'lucide-react-native'
import { useRouter } from 'expo-router'
import { colors as tokens } from '@active-bite-ai/tokens'
import { updateProfile } from '@active-bite-ai/database'
import { useAuth } from '../../contexts/AuthContext'

const TERMS_URL = 'https://github.com/ivinroekiman/Active-Bite-AI/blob/main/docs/legal/terms.md'
const PRIVACY_URL = 'https://github.com/ivinroekiman/Active-Bite-AI/blob/main/docs/legal/privacy.md'
const DISCLAIMER_URL = 'https://github.com/ivinroekiman/Active-Bite-AI/blob/main/docs/legal/health-disclaimer.md'

export default function LegalConsentScreen() {
  const router = useRouter()
  const { currentUser, refreshProfile } = useAuth()

  const [acceptTerms, setAcceptTerms] = useState(false)
  const [acceptPrivacy, setAcceptPrivacy] = useState(false)
  const [ackDisclaimer, setAckDisclaimer] = useState(false)
  const [marketingOptIn, setMarketingOptIn] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const canContinue = acceptTerms && acceptPrivacy && ackDisclaimer && !!currentUser?.id

  const handleContinue = async () => {
    if (!currentUser?.id || !canContinue || submitting) return
    try {
      setSubmitting(true)
      await updateProfile(currentUser.id, {
        terms_accepted_at: new Date().toISOString(),
        terms_version: 'v1',
        marketing_opt_in: marketingOptIn,
      })
      await refreshProfile()
      router.push('/onboarding/units')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%">
          <Heading size="xl" mb="$10">
            Legal Consent
          </Heading>
          <Text size="md" mb="$6">
            Please review and accept the following to continue.
          </Text>

          <VStack space="lg">
            <HStack alignItems="center" justifyContent="space-between">
              <VStack space="xs" flex={1} mr="$3">
                <Text size="md">Terms of Service</Text>
                <Button variant="link" onPress={() => Linking.openURL(TERMS_URL)}>
                  <ButtonText>View</ButtonText>
                </Button>
              </VStack>
              <Checkbox value="terms" isChecked={acceptTerms} onChange={setAcceptTerms}>
                <CheckboxIndicator mr="$2">
                  <CheckboxIcon as={CheckIcon} />
                </CheckboxIndicator>
                <CheckboxLabel>I agree</CheckboxLabel>
              </Checkbox>
            </HStack>

            <HStack alignItems="center" justifyContent="space-between">
              <VStack space="xs" flex={1} mr="$3">
                <Text size="md">Privacy Policy</Text>
                <Button variant="link" onPress={() => Linking.openURL(PRIVACY_URL)}>
                  <ButtonText>View</ButtonText>
                </Button>
              </VStack>
              <Checkbox value="privacy" isChecked={acceptPrivacy} onChange={setAcceptPrivacy}>
                <CheckboxIndicator mr="$2">
                  <CheckboxIcon as={CheckIcon} />
                </CheckboxIndicator>
                <CheckboxLabel>I agree</CheckboxLabel>
              </Checkbox>
            </HStack>

            <HStack alignItems="center" justifyContent="space-between">
              <VStack space="xs" flex={1} mr="$3">
                <Text size="md">Health Disclaimer</Text>
                <Button variant="link" onPress={() => Linking.openURL(DISCLAIMER_URL)}>
                  <ButtonText>View</ButtonText>
                </Button>
              </VStack>
              <Checkbox value="disclaimer" isChecked={ackDisclaimer} onChange={setAckDisclaimer}>
                <CheckboxIndicator mr="$2">
                  <CheckboxIcon as={CheckIcon} />
                </CheckboxIndicator>
                <CheckboxLabel>I acknowledge</CheckboxLabel>
              </Checkbox>
            </HStack>

            <HStack alignItems="center" justifyContent="space-between" mt="$2">
              <Text size="sm" flexShrink={1} mr="$3">
                Receive product updates and tips via email
              </Text>
              <Switch value={marketingOptIn} onValueChange={setMarketingOptIn} accessibilityLabel="Marketing opt-in" />
            </HStack>
          </VStack>

          <Button
            action="primary"
            size="lg"
            onPress={handleContinue}
            isDisabled={submitting || !canContinue}
            style={styles.button}
            mt="$10"
          >
            <ButtonText color="$white" fontWeight="$bold">
              Continue
            </ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  )
}

const styles = StyleSheet.create({
  button: {
    width: '100%',
  },
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
})
