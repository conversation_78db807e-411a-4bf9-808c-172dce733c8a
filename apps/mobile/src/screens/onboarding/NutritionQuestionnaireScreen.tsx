import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, ButtonText, Text, Heading, Center, VStack, Checkbox, CheckboxGroup, CheckboxIndicator, CheckboxIcon, CheckboxLabel, CheckIcon, Input, InputField, FormControl, FormControlLabel, FormControlLabelText, Radio, RadioGroup, RadioIndicator, RadioIcon, RadioLabel, CircleIcon } from '@gluestack-ui/themed';
import { colors as tokens } from '@active-bite-ai/tokens';
import { useRouter } from 'expo-router';
import { saveOnboardingData } from '../../utils/onboardingStorage';

const NutritionQuestionnaireScreen = () => {
  const router = useRouter();
  const [dietaryPreferences, setDietaryPreferences] = useState<string[]>([]);
  const [allergies, setAllergies] = useState<string[]>([]);
  const [budget, setBudget] = useState('');
  const [cuisineTypes, setCuisineTypes] = useState<string[]>([]);
  const [cookingDifficulty, setCookingDifficulty] = useState('');

  const handleContinue = async () => {
    await saveOnboardingData({
      dietaryPreferences,
      allergies,
      budget,
      cuisineTypes,
      cookingDifficulty,
    });
    router.push('/onboarding/ai-persona');
  };

  const dietaryPreferenceOptions = [
    { id: 'vegetarian', label: 'Vegetarian' },
    { id: 'vegan', label: 'Vegan' },
    { id: 'pescatarian', label: 'Pescatarian' },
    { id: 'keto', label: 'Keto' },
    { id: 'paleo', label: 'Paleo' },
    { id: 'gluten-free', label: 'Gluten-Free' },
    { id: 'dairy-free', label: 'Dairy-Free' },
  ];

  const cuisineOptions = [
    { id: 'italian', label: 'Italian' },
    { id: 'mexican', label: 'Mexican' },
    { id: 'asian', label: 'Asian' },
    { id: 'mediterranean', label: 'Mediterranean' },
    { id: 'american', label: 'American' },
    { id: 'indian', label: 'Indian' },
  ];

  const difficultyOptions = [
    { id: 'beginner', label: 'Beginner (Simple recipes)' },
    { id: 'intermediate', label: 'Intermediate (Moderate complexity)' },
    { id: 'advanced', label: 'Advanced (Complex recipes)' },
  ];

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%">
          <Heading size="xl" mb="$10">Nutrition Preferences</Heading>
          <Text size="md" mb="$6">
            Help us create the perfect meal plan for you
          </Text>
          
          {/* Dietary Preferences */}
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Dietary Preferences</FormControlLabelText>
            </FormControlLabel>
            <CheckboxGroup 
              value={dietaryPreferences}
              onChange={(values: string[]) => setDietaryPreferences(values)}
            >
              {dietaryPreferenceOptions.map((option) => (
                <Checkbox key={option.id} value={option.id} size="md" mb="$2">
                  <CheckboxIndicator mr="$2">
                    <CheckboxIcon as={CheckIcon} />
                  </CheckboxIndicator>
                  <CheckboxLabel>{option.label}</CheckboxLabel>
                </Checkbox>
              ))}
            </CheckboxGroup>
          </FormControl>
          
          {/* Allergies */}
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Allergies (Optional)</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField 
                placeholder="e.g., nuts, shellfish, eggs"
                value={allergies.join(', ')}
                onChangeText={(value: string) => setAllergies(value.split(',').map(item => item.trim()))}
              />
            </Input>
          </FormControl>
          
          {/* Budget */}
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Weekly Food Budget</FormControlLabelText>
            </FormControlLabel>
            <RadioGroup 
              value={budget}
              onChange={(value: string) => setBudget(value)}
            >
              <Radio value="low" size="md" mb="$2">
                <RadioIndicator mr="$2">
                  <RadioIcon as={CircleIcon} />
                </RadioIndicator>
                <RadioLabel>Low ($50 or less)</RadioLabel>
              </Radio>
              <Radio value="medium" size="md" mb="$2">
                <RadioIndicator mr="$2">
                  <RadioIcon as={CircleIcon} />
                </RadioIndicator>
                <RadioLabel>Medium ($50-$100)</RadioLabel>
              </Radio>
              <Radio value="high" size="md" mb="$2">
                <RadioIndicator mr="$2">
                  <RadioIcon as={CircleIcon} />
                </RadioIndicator>
                <RadioLabel>High ($100+)</RadioLabel>
              </Radio>
            </RadioGroup>
          </FormControl>
          
          {/* Cuisine Types */}
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Preferred Cuisine Types</FormControlLabelText>
            </FormControlLabel>
            <CheckboxGroup 
              value={cuisineTypes}
              onChange={(values: string[]) => setCuisineTypes(values)}
            >
              {cuisineOptions.map((option) => (
                <Checkbox key={option.id} value={option.id} size="md" mb="$2">
                  <CheckboxIndicator mr="$2">
                    <CheckboxIcon as={CheckIcon} />
                  </CheckboxIndicator>
                  <CheckboxLabel>{option.label}</CheckboxLabel>
                </Checkbox>
              ))}
            </CheckboxGroup>
          </FormControl>
          
          {/* Cooking Difficulty */}
          <FormControl mb="$6">
            <FormControlLabel>
              <FormControlLabelText>Cooking Experience Level</FormControlLabelText>
            </FormControlLabel>
            <RadioGroup 
              value={cookingDifficulty}
              onChange={(value: string) => setCookingDifficulty(value)}
            >
              {difficultyOptions.map((option) => (
                <Radio key={option.id} value={option.id} size="md" mb="$2">
                  <RadioIndicator mr="$2">
                    <RadioIcon as={CircleIcon} />
                  </RadioIndicator>
                  <RadioLabel>{option.label}</RadioLabel>
                </Radio>
              ))}
            </RadioGroup>
          </FormControl>
          
          <Button
            action="primary"
            size="lg"
            onPress={handleContinue}
            mt="$6"
          >
            <ButtonText color="$white" fontWeight="$bold">Continue</ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
});

export default NutritionQuestionnaireScreen;
