import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Heading, Center, VStack, Spinner, HStack } from '@gluestack-ui/themed';
import { colors as tokens } from '@active-bite-ai/tokens';
import { useRouter } from 'expo-router';

const OnboardingCompleteScreen = () => {
  const router = useRouter();

  useEffect(() => {
    // Simulate plan generation delay
    const timer = setTimeout(() => {
      // Navigate to the main app
      router.replace('/(tabs)/home');
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" alignItems="center">
          <Heading size="2xl" mb="$6">Setup Complete!</Heading>
          <Text size="lg" textAlign="center" mb="$10">
            Your AI coach is creating your first personalized plan
          </Text>
          
          <HStack space="md" alignItems="center">
            <Spinner size="large" />
            <Text size="md">Generating your plan...</Text>
          </HStack>
          
          <Text size="sm" color="$text500" mt="$10" textAlign="center">
            This may take a few moments
          </Text>
        </VStack>
      </Center>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
});

export default OnboardingCompleteScreen;
