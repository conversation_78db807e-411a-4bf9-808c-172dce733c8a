import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, ButtonText, Text, Heading, Center, VStack, Radio, RadioGroup, RadioIndicator, RadioIcon, RadioLabel, CircleIcon } from '@gluestack-ui/themed';
import { colors as tokens } from '@active-bite-ai/tokens';
import { useRouter } from 'expo-router';
import { saveOnboardingData } from '../../utils/onboardingStorage';

const PlanSelectionScreen = () => {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState('both');

  const handleContinue = async () => {
    await saveOnboardingData({ planSelection: selectedPlan as 'workout' | 'nutrition' | 'both' });
    router.push('/onboarding/ai-persona');
  };

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%">
          <Heading size="xl" mb="$10">Choose Your Plan</Heading>
          <Text size="md" mb="$6">
            Select which aspects of your health you’d like to focus on
          </Text>
          
          <RadioGroup 
            value={selectedPlan}
            onChange={setSelectedPlan}
          >
            <Radio value="workout" size="md" mb="$4">
              <RadioIndicator mr="$2">
                <RadioIcon as={CircleIcon} />
              </RadioIndicator>
              <RadioLabel>Workout Plan Only</RadioLabel>
            </Radio>
            <Radio value="nutrition" size="md" mb="$4">
              <RadioIndicator mr="$2">
                <RadioIcon as={CircleIcon} />
              </RadioIndicator>
              <RadioLabel>Nutrition Plan Only</RadioLabel>
            </Radio>
            <Radio value="both" size="md" mb="$4">
              <RadioIndicator mr="$2">
                <RadioIcon as={CircleIcon} />
              </RadioIndicator>
              <RadioLabel>Both Workout & Nutrition</RadioLabel>
            </Radio>
          </RadioGroup>
          
          <Button
            action="primary"
            size="lg"
            onPress={handleContinue}
            mt="$6"
          >
            <ButtonText color="$white" fontWeight="$bold">
              Continue
            </ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
});

export default PlanSelectionScreen;
