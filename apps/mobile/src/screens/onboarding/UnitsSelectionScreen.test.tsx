import React from 'react'
import { render, fireEvent, waitFor } from '@testing-library/react-native'
import UnitsSelectionScreen from './UnitsSelectionScreen'

// Mock expo-router navigation
const mockPush = jest.fn()
jest.mock('expo-router', () => ({ useRouter: () => ({ push: mockPush }) }))

// Mock db
jest.mock('@active-bite-ai/database', () => ({
  updateProfile: jest.fn().mockResolvedValue({ data: {}, error: null }),
}))
const { updateProfile } = require('@active-bite-ai/database')
// Mock tokens to avoid undefined
jest.mock('@active-bite-ai/tokens', () => ({ colors: { surface: '#fff' } }))

// Mock auth
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({ currentUser: { id: 'u1' }, refreshProfile: jest.fn() }),
}))

describe('UnitsSelectionScreen', () => {
  beforeEach(() => { jest.clearAllMocks() })

  it('saves units and navigates to complete', async () => {
    const { getByText } = render(<UnitsSelectionScreen />)

    fireEvent.press(getByText('Continue'))

    await waitFor(() => expect(updateProfile).toHaveBeenCalled())
    await waitFor(() => expect(mockPush).toHaveBeenCalledWith('/onboarding/complete'))
  })
})

