import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Text, Heading, Center, VStack, ButtonText } from '@gluestack-ui/themed';
import { colors as tokens, spacing } from '@active-bite-ai/tokens';
import { useRouter } from 'expo-router';

const WelcomeScreen = () => {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/onboarding/language');
  };

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" alignItems="center">
          <Heading size="2xl">Welcome to Active Bite AI</Heading>
          <Text size="lg" textAlign="center">
            Your personalized fitness and nutrition companion
          </Text>
          <Button
            action="primary"
            size="lg"
            onPress={handleGetStarted}
            style={styles.button}
          >
            <ButtonText color="$white" fontWeight="$bold">
              Get Started
            </ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  );
};

const styles = StyleSheet.create({
  button: {
    marginTop: spacing.xl,
    width: '80%',
  },
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
});

export default WelcomeScreen;
