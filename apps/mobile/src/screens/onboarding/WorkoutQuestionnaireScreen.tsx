import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, ButtonText, Text, Heading, Center, VStack, Checkbox, CheckboxGroup, CheckboxIndicator, CheckboxIcon, CheckboxLabel, CheckIcon, Input, InputField, FormControl, FormControlLabel, FormControlLabelText } from '@gluestack-ui/themed';
import { colors as tokens } from '@active-bite-ai/tokens';
import { useRouter } from 'expo-router';
import { saveOnboardingData } from '../../utils/onboardingStorage';

const WorkoutQuestionnaireScreen = () => {
  const router = useRouter();
  const [experienceLevel, setExperienceLevel] = useState('');
  const [knownInjuries, setKnownInjuries] = useState('');
  const [preferredExerciseTypes, setPreferredExerciseTypes] = useState<string[]>([]);
  const [availableEquipment, setAvailableEquipment] = useState<string[]>([]);

  const handleContinue = async () => {
    await saveOnboardingData({
      experienceLevel,
      knownInjuries: knownInjuries ? knownInjuries.split(',').map(item => item.trim()) : [],
      preferredExerciseTypes,
      availableEquipment,
    });
    router.push('/onboarding/nutrition');
  };

  const experienceOptions = [
    { id: 'beginner', label: 'Beginner' },
    { id: 'intermediate', label: 'Intermediate' },
    { id: 'advanced', label: 'Advanced' },
  ];

  const exerciseTypeOptions = [
    { id: 'cardio', label: 'Cardio' },
    { id: 'strength', label: 'Strength Training' },
    { id: 'yoga', label: 'Yoga' },
    { id: 'pilates', label: 'Pilates' },
    { id: 'hiit', label: 'HIIT' },
    { id: 'crossfit', label: 'CrossFit' },
  ];

  const equipmentOptions = [
    { id: 'dumbbells', label: 'Dumbbells' },
    { id: 'barbell', label: 'Barbell' },
    { id: 'kettlebell', label: 'Kettlebell' },
    { id: 'resistance-bands', label: 'Resistance Bands' },
    { id: 'pull-up-bar', label: 'Pull-up Bar' },
    { id: 'bench', label: 'Bench' },
    { id: 'none', label: 'No Equipment' },
  ];

  return (
    <View style={styles.container}>
      <Center flex={1}>
        <VStack space="md" w="80%">
          <Heading size="xl" mb="$10">Workout Preferences</Heading>
          <Text size="md" mb="$6">
            Help us create the perfect workout plan for you
          </Text>
          
          {/* Experience Level */}
          <FormControl mb="$6">
            <FormControlLabel mb="$2">
              <FormControlLabelText>Experience Level</FormControlLabelText>
            </FormControlLabel>
            <CheckboxGroup 
              value={[experienceLevel]}
              onChange={(values: string[]) => setExperienceLevel(values[0] || '')}
            >
              <VStack space="sm">
                {experienceOptions.map((option) => (
                  <Checkbox key={option.id} value={option.id} size="md">
                    <CheckboxIndicator mr="$2">
                      <CheckboxIcon as={CheckIcon} />
                    </CheckboxIndicator>
                    <CheckboxLabel>{option.label}</CheckboxLabel>
                  </Checkbox>
                ))}
              </VStack>
            </CheckboxGroup>
          </FormControl>
          
          {/* Known Injuries */}
          <FormControl mb="$6">
            <FormControlLabel mb="$2">
              <FormControlLabelText>Known Injuries (Optional)</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField 
                placeholder="e.g., knee injury, back pain"
                value={knownInjuries}
                onChangeText={setKnownInjuries}
              />
            </Input>
          </FormControl>
          
          {/* Preferred Exercise Types */}
          <FormControl mb="$6">
            <FormControlLabel mb="$2">
              <FormControlLabelText>Preferred Exercise Types</FormControlLabelText>
            </FormControlLabel>
            <CheckboxGroup 
              value={preferredExerciseTypes}
              onChange={setPreferredExerciseTypes}
            >
              <VStack space="sm">
                {exerciseTypeOptions.map((option) => (
                  <Checkbox key={option.id} value={option.id} size="md">
                    <CheckboxIndicator mr="$2">
                      <CheckboxIcon as={CheckIcon} />
                    </CheckboxIndicator>
                    <CheckboxLabel>{option.label}</CheckboxLabel>
                  </Checkbox>
                ))}
              </VStack>
            </CheckboxGroup>
          </FormControl>
          
          {/* Available Equipment */}
          <FormControl mb="$6">
            <FormControlLabel mb="$2">
              <FormControlLabelText>Available Equipment</FormControlLabelText>
            </FormControlLabel>
            <CheckboxGroup 
              value={availableEquipment}
              onChange={setAvailableEquipment}
            >
              <VStack space="sm">
                {equipmentOptions.map((option) => (
                  <Checkbox key={option.id} value={option.id} size="md">
                    <CheckboxIndicator mr="$2">
                      <CheckboxIcon as={CheckIcon} />
                    </CheckboxIndicator>
                    <CheckboxLabel>{option.label}</CheckboxLabel>
                  </Checkbox>
                ))}
              </VStack>
            </CheckboxGroup>
          </FormControl>
          
          <Button
            action="primary"
            size="lg"
            onPress={handleContinue}
            mt="$6"
          >
            <ButtonText color="$white" fontWeight="$bold">
              Continue
            </ButtonText>
          </Button>
        </VStack>
      </Center>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
});

export default WorkoutQuestionnaireScreen;
