import { config as baseConfig } from '@gluestack-ui/config'
import { colors } from '@active-bite-ai/tokens'

// Extend base Gluestack config with our brand tokens
export const config = {
  ...baseConfig,
  tokens: {
    ...baseConfig.tokens,
    colors: {
      ...baseConfig.tokens?.colors,
      // Map brand tokens; keep base semantic tokens so components work
      primary500: colors.primary,
      secondary500: colors.secondary,
      accent500: colors.accent,
      background50: colors.background,
      surface: colors.surface,
      text800: colors.text,
      text500: colors.textSecondary,
      error500: colors.error,
      success500: colors.success,
      warning500: colors.warning,
    },
  },
} as const

export default config

