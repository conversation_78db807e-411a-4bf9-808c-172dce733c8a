import { supabase, upsertOnboardingGeneral, upsertOnboardingNutrition, upsertOnboardingPreferences, upsertOnboardingWorkout } from '@active-bite-ai/database';
import { OnboardingData, getOnboardingData, clearOnboardingData } from './onboardingStorage';
import { TablesInsert } from '@active-bite-ai/database/src/types';

/**
 * Transfer onboarding data from local storage to Supabase after authentication
 * @param userId - The authenticated user's ID
 */
export const transferOnboardingData = async (userId: string): Promise<boolean> => {
  try {
    // Get onboarding data from local storage
    const onboardingData: OnboardingData | null = await getOnboardingData();

    // Always ensure profile row exists/updates
    const profileData: TablesInsert<'profiles'> = {
      id: userId,
      updated_at: new Date().toISOString(),
      username: null,
      full_name: null,
      avatar_url: null,
      website: null,
    };

    const { error: profileError } = await supabase
      .from('profiles')
      .upsert(profileData);

    if (profileError) {
      console.error('Error ensuring profile row:', profileError);
      return false;
    }

    if (!onboardingData) {
      console.log('No local onboarding data to transfer');
      return true;
    }

    // Upsert onboarding tables selectively based on available fields
    const promises: Promise<unknown>[] = []

    if (
      onboardingData.planSelection !== undefined ||
      onboardingData.aiPersona !== undefined
    ) {
      promises.push(upsertOnboardingPreferences(userId, {
        plan_selection: onboardingData.planSelection ?? null,
        ai_persona: onboardingData.aiPersona ?? null,
      }))
    }

    if (
      onboardingData.experienceLevel !== undefined ||
      onboardingData.knownInjuries !== undefined ||
      onboardingData.preferredExerciseTypes !== undefined ||
      onboardingData.availableEquipment !== undefined
    ) {
      promises.push(upsertOnboardingWorkout(userId, {
        experience_level: onboardingData.experienceLevel ?? null,
        known_injuries: onboardingData.knownInjuries ?? null,
        preferred_exercise_types: onboardingData.preferredExerciseTypes ?? null,
        available_equipment: onboardingData.availableEquipment ?? null,
      }))
    }

    if (
      onboardingData.dietaryPreferences !== undefined ||
      onboardingData.allergies !== undefined ||
      onboardingData.budget !== undefined ||
      onboardingData.cuisineTypes !== undefined ||
      onboardingData.cookingDifficulty !== undefined
    ) {
      promises.push(upsertOnboardingNutrition(userId, {
        dietary_preferences: onboardingData.dietaryPreferences ?? null,
        allergies: onboardingData.allergies ?? null,
        budget: onboardingData.budget ?? null,
        cuisine_types: onboardingData.cuisineTypes ?? null,
        cooking_difficulty: onboardingData.cookingDifficulty ?? null,
      }))
    }

    if (
      onboardingData.age !== undefined ||
      onboardingData.height !== undefined ||
      onboardingData.weight !== undefined
    ) {
      promises.push(upsertOnboardingGeneral(userId, {
        age: onboardingData.age ?? null,
        height: onboardingData.height ?? null,
        weight: onboardingData.weight ?? null,
      }))
    }

    await Promise.all(promises)

    // Clear local storage after successful transfer
    await clearOnboardingData();

    console.log('Onboarding data successfully transferred to Supabase');
    return true;
  } catch (error) {
    console.error('Error during data transfer:', error);
    return false;
  }
};
