import AsyncStorage from '@react-native-async-storage/async-storage';

export interface OnboardingData {
  // General profile info
  age?: number;
  height?: number;
  weight?: number;
  primaryGoals?: string[];
  language?: string;
  
  // Plan preferences
  planSelection?: 'workout' | 'nutrition' | 'both';
  
  // Workout-specific info
  experienceLevel?: string;
  knownInjuries?: string[];
  preferredExerciseTypes?: string[];
  availableEquipment?: string[];
  
  // Nutrition-specific info
  dietaryPreferences?: string[];
  allergies?: string[];
  budget?: string;
  cuisineTypes?: string[];
  cookingDifficulty?: string;
  
  // AI persona selection
  aiPersona?: string;
}

const ONBOARDING_DATA_KEY = 'onboarding_data';

export const saveOnboardingData = async (data: Partial<OnboardingData>) => {
  try {
    const existingData = await getOnboardingData();
    const updatedData = { ...existingData, ...data };
    await AsyncStorage.setItem(ONBOARDING_DATA_KEY, JSON.stringify(updatedData));
    return true;
  } catch (error) {
    console.error('Error saving onboarding data:', error);
    return false;
  }
};

export const getOnboardingData = async (): Promise<OnboardingData | null> => {
  try {
    const data = await AsyncStorage.getItem(ONBOARDING_DATA_KEY);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Error retrieving onboarding data:', error);
    return null;
  }
};

export const clearOnboardingData = async () => {
  try {
    await AsyncStorage.removeItem(ONBOARDING_DATA_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing onboarding data:', error);
    return false;
  }
};
