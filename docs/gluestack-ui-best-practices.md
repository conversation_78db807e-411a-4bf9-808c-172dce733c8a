# Gluestack UI Best Practices (React Native / Expo)

This guide summarizes practical patterns and pitfalls for using Gluestack UI in our Expo mobile app, aligned with our org rules (design tokens, TypeScript strict, accessibility, and performance). References are included at the end.

## 1) Provider & Configuration
- __Use the official config__: Wrap the app with `GluestackUIProvider` and pass `config` from `@gluestack-ui/config`.
- __Single provider at root__: Place it in `apps/mobile/App.tsx` so all screens share the theme.
- __Theme tokens over raw values__: Prefer tokens in props (e.g., `color="$white"`, `mb="$6"`) to keep consistency with our design system.

Example:
```tsx
import { GluestackUIProvider } from '@gluestack-ui/themed'
import { config } from '@gluestack-ui/config'

export function Providers({ children }: { children: React.ReactNode }) {
  return <GluestackUIProvider config={config}>{children}</GluestackUIProvider>
}
```

References: GluestackUIProvider with `config` [docs]

## 2) Slot Components (Composition)
- __Buttons__: Use `ButtonText` as the child of `Button` (not `Text`).
- __Radios__: Compose `Radio` with `RadioIndicator`, `RadioIcon`, and `RadioLabel` inside a `RadioGroup`.
- __Checkboxes__: Compose `Checkbox` with `CheckboxIndicator`, `CheckboxIcon`, and `CheckboxLabel` inside a `CheckboxGroup`.
- __FormControl__: Wrap inputs with `FormControl`, `FormControlLabel`, `FormControlLabelText`, and (optionally) helper/error components.

Examples:
```tsx
// Button
<Button action="primary"><ButtonText>Continue</ButtonText></Button>

// Radio
<RadioGroup value={value} onChange={setValue}>
  <Radio value="optA">
    <RadioIndicator>
      <RadioIcon />
    </RadioIndicator>
    <RadioLabel>Option A</RadioLabel>
  </Radio>
</RadioGroup>

// Checkbox
<CheckboxGroup value={values} onChange={setValues}>
  <Checkbox value="opt1">
    <CheckboxIndicator>
      <CheckboxIcon />
    </CheckboxIndicator>
    <CheckboxLabel>Opt 1</CheckboxLabel>
  </Checkbox>
</CheckboxGroup>

// Input inside FormControl
<FormControl>
  <FormControlLabel>
    <FormControlLabelText>Age</FormControlLabelText>
  </FormControlLabel>
  <Input>
    <InputField keyboardType="numeric" />
  </Input>
</FormControl>
```

References: ButtonText in Tooltip example, Radio/Checkbox composition [docs]

## 3) TypeScript & Strictness
- __Exact optional props__: Some libraries (e.g., React Navigation v7) use strict optional typing. If `Stack.Navigator` requires `id`, pass `id={undefined}` when needed to satisfy types.
- __Children types__: Follow Gluestack’s slot components; incorrect children (e.g., `Text` inside `Button`) cause TS errors.
- __Input values__: Convert string inputs to numbers before saving to typed stores (e.g., `Number(age)` for `age?: number`).

## 4) Tokens and Styling
- __No raw values__: Use tokens from `packages/tokens/` and Gluestack props like `px="$4"`, `color="$textLight0"`, `rounded="$lg"`.
- __Variant-first styling__: Prefer component variants and state props over inline styles. Keep overrides minimal to preserve theme consistency.
- __Consistent spacing and typography__: Ensure spacing/typography is driven by tokens and used consistently across screens.

## 5) Accessibility (A11y)
- __Labels__: Always provide readable labels with `RadioLabel` / `CheckboxLabel` and `FormControlLabelText`.
- __State announcements__: Gluestack components support screen readers; ensure checked/unchecked is conveyed via proper composition.
- __Focus management__: Use built-in focus states; avoid removing focus outlines. Prefer `onFocus`/`onBlur` only for logic, not to hide focus.

## 6) Performance & Footprint
- __Composition over custom__: Reuse provided primitives; avoid reimplementing state styles.
- __Avoid heavy inline styles__: Stick with themed props for zero-runtime class generation and better caching.
- __Batch re-renders__: Keep forms controlled but avoid unnecessary state churn; memoize expensive subtrees if needed.

## 7) Forms & Validation Patterns
- __Use `FormControl` wrappers__: For each field, include label, control, and error/helper text if applicable.
- __Native keyboard types__: Set `keyboardType` (numeric/email) and validate accordingly.
- __Data normalization__: Cast to correct types when persisting (e.g., numbers for numeric fields).

## 8) Navigation & Layout Integration
- __Navigator typing__: With strict TS settings, provide required props (`id={undefined}` workaround) to avoid type errors.
- __Layout spacing__: Use `VStack`/`HStack` with tokenized spacing (`space="md"`) rather than manual margins.
- __Header & theming__: Keep screen titles and header visibility in navigator options; don’t embed faux headers in content.

## 9) Testing Tips
- __@testing-library/react-native__: Query by role/label where possible (labels from `*Label` components).
- __Snapshot minimalism__: Prefer interaction tests over large snapshots; assert state changes via accessibility states (checked, disabled).

## 10) Common Pitfalls
- __Using plain Text in Button__: Always use `ButtonText`.
- __Missing provider config__: Ensure `@gluestack-ui/config` is installed and used at the root.
- __Bypassing tokens__: Avoid raw hex/pixel values; use tokens.
- __Incorrect composition__: Radio/Checkbox must include Indicator/Icon/Label as children; otherwise styles and a11y degrade.

---

## 11) Using Tokens in React Native StyleSheet
- __When to use__: If you need a React Native `StyleSheet` (e.g., wrapping `View`, `ScrollView`, `Modal` overlay), import tokens from our `packages/tokens`.
- __Do__: `import { colors as tokens, spacing, borderRadius } from '@active-bite-ai/tokens'` and reference them in `StyleSheet.create`.
- __Don’t__: Hardcode `'#fff'`, `'#000'`, numeric pixel values, or literal radii.

Example:
```tsx
const styles = StyleSheet.create({
  container: {
    backgroundColor: tokens.surface,
    flex: 1,
  },
  card: {
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
  },
})
```

## 12) No Inline Styles on Gluestack Components
- __Prefer themed props__: Use `mb="$6"`, `w="$full"`, `variant="outline"`, `action="primary"` instead of `style={{ marginBottom: 24 }}`.
- __If custom layout needed__: Wrap with a plain `View` using `StyleSheet` + tokens rather than applying inline style objects directly to Gluestack components.
- __Color overrides__: Prefer semantic Gluestack tokens on props (e.g., `color="$text500"`) and only override when necessary.

## 13) Deriving rgba From Tokens (Overlays)
- __Avoid__: Hardcoded strings like `rgba(0,0,0,0.5)`.
- __Prefer__: Derive rgba from token colors to keep overlays consistent with the theme.

Example helper:
```ts
const hexToRgba = (hex: string, alpha: number) => {
  const sanitized = hex.replace('#', '')
  const bigint = parseInt(sanitized, 16)
  const r = (bigint >> 16) & 255
  const g = (bigint >> 8) & 255
  const b = bigint & 255
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

const OVERLAY_BG = hexToRgba(tokens.text, 0.5)
```

## 14) Quick Checklist (PR-Ready)
- __Provider__: `GluestackUIProvider` with `@gluestack-ui/config` at app root.
- __Slots__: `ButtonText` inside `Button`; proper Radio/Checkbox composition.
- __Tokens__: No raw hex/pixels; use `@active-bite-ai/tokens` in `StyleSheet` and Gluestack props for components.
- __No inline styles__: Prefer themed props or `StyleSheet` with tokens.
- __Types__: Numeric inputs parsed before persist; navigation props typed (use `id={undefined}` when required by types).
- __A11y__: Labels present; state conveyed; focus states intact.

## References & Citations
- Gluestack UI Getting Started and Provider setup:
  - GluestackUIProvider with `config` (themed README):
    - https://github.com/gluestack/gluestack-ui/blob/main/packages/themed/README.md#_snippet_1
  - Config package basic setup:
    - https://github.com/gluestack/gluestack-ui/blob/main/packages/config/README.md#_snippet_1
  - Introduction (theming tokens and config):
    - https://gluestack.io/ui/docs/home/<USER>/introduction
- Components and composition patterns:
  - All components:
    - https://gluestack.io/ui/docs/components/all-components
  - Checkbox (Indicator/Icon/Label, within Group):
    - https://gluestack.io/ui/docs/components/checkbox
  - Radio (Indicator/Icon/Label, within Group):
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/components/Radio/index.nw.stories.mdx#_snippet_0
  - Tooltip example showing Button + ButtonText:
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/components/Tooltip/index.themed.stories.mdx#_snippet_0
- Accessibility and states:
  - Radio screen reader/focus notes:
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/components/Radio/index.nw.stories.mdx#_snippet_8
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/components/Radio/index.nw.stories.mdx#_snippet_9
- Performance benchmarks:
  - Overview benchmarks:
    - https://github.com/gluestack/gluestack-ui/blob/main/example/storybook-nativewind/src/home/<USER>/benchmarks/index.stories.mdx#_qa_5

Short excerpts:
- “Write this code snippet at the root of your application … `<GluestackUIProvider config={config}>`” (themed README).
- “Theming Tokens: gluestack-ui takes a config file in the GluestackUIProvider, which contains theme tokens … You can install and import the config as a module.” (Introduction).
- “Checkbox … contains all Checkbox related layout style props and actions … Inherits properties of React Native’s View component.” (Checkbox docs).
