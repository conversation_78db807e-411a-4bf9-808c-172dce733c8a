# Mobile Application Flow & Navigation

This document outlines the user flow, navigation structure, and screen-by-screen functionality for the Active Bite AI mobile application.

## 1. Main Navigation (Tab Bar)

The primary navigation will be a five-tab footer menu:

1.  **Home:** The main dashboard. The default screen upon opening the app post-onboarding.
2.  **AI Coach:** The intelligent coaching assistant that provides personalized advice, questionnaires, and recommendations.
3.  **Nutrition:** A dedicated section for all meal-related plans and content.
4.  **Exercise:** A dedicated section for all workout-related plans and content.
5.  **Profile:** The user's personal space for settings, account management, and support.

---

## 2. Key User Flows

### Flow 1: First-Time User Onboarding

This flow is triggered once a user creates an account and logs in for the first time.

- **Screen 1: Welcome**
  - **Content:** A brief, engaging welcome message explaining the value of the onboarding process.
  - **Action:** Button `Let's Get Started`.

- **Screen 2: General Questionnaire**
  - **Content:** Form fields for `Age`, `Height`, `Weight`, `Primary Goals` (e.g., lose weight, gain muscle).
  - **Action:** Button `Next`.

- **Screen 3: Plan Selection**
  - **Content:** Asks the user if they want `Workout Plans`, `Meal Plans`, or `Both`.
  - **Action:** Tapping an option proceeds to the next relevant questionnaire.

- **Screen 4: Workout Questionnaire (Conditional)**
  - **Content:** Form fields for `Experience Level`, `Known Injuries`, `Preferred Exercise Types`, `Available Equipment`.
  - **Action:** Button `Next`.

- **Screen 5: Nutrition Questionnaire (Conditional)**
  - **Content:** Form fields for `Dietary Preferences`, `Allergies`, `Budget`, `Cuisine Types`, `Cooking Difficulty`.
  - **Action:** Button `Next`.

- **Screen 6: AI Persona Selection**
  - **Content:** A visually engaging screen allowing the user to select their preferred AI coach personality (e.g., 'Motivator', 'Drill Sergeant').
  - **Action:** Tapping a persona and then `Finish Setup`.

- **Screen 7: Onboarding Complete / Plan Generation**
  - **Content:** A loading/confirmation screen that says "Your AI coach is creating your first personalized plan!"
  - **Action:** Automatically navigates to the `Home` tab once the initial plan is ready.

---

### Flow 2: Daily Plan Interaction

This flow describes the primary daily loop for an engaged user.

- **Screen: Home (Dashboard)**
  - **Purpose:** To provide the user with a clear, actionable summary of their plan for the current day and a snapshot of their weekly progress.
  - **Content Breakdown:**
    - **Header:** A personalized greeting (e.g., "Good morning, Jane!").
    - **Today's Workout Card:** A large, tappable card showing the name of the day's workout. Visually changes to a 'Completed' state after the workout is done.
    - **Today's Nutrition Card:** An overview of the day's meals (Breakfast, Lunch, Dinner, Snack) with checkboxes to mark them as 'Eaten'.
    - **AI Coach's Corner:** A small card with a daily tip or motivational quote from the AI coach.
    - **Weekly Consistency Tracker:** A visual representation (e.g., 7 circles for 7 days) of the user's workout adherence for the current week.
  - **Action:** Tapping the workout card navigates to the `Workout Detail` screen. Tapping a meal navigates to the `Recipe Detail` screen.

- **Screen: Exercise Tab**
  - **Purpose:** To provide a clear, day-by-day view of the user's workout schedule.
  - **Content Breakdown:**
    - **Header:** Date navigator with arrows and a calendar icon to select a day.
    - **Today's Workout Card:** A large card showing the workout name, estimated duration, and total exercises. A prominent 'Start Workout' button is the primary action.
    - **Weekly Schedule:** A visual overview of the current week, highlighting workout days and rest days.
    - **Rest Day State:** If the selected day is a rest day, the card will display a message encouraging recovery.
  - **Action:** Tapping 'Start Workout' navigates to the `Workout Detail` screen.

- **Screen: Workout Detail**
  - **Content:** Lists all exercises for the day's workout with images/thumbnails. Each item shows sets, reps, and a play icon.
  - **Action:** Tapping an exercise opens the `Exercise Player` screen. A `Start Workout` button at the top and a `Mark as Complete` button at the bottom.

- **Screen: Exercise Player (Modal or Full Screen)**
  - **Content:** Displays the instructional video for the selected exercise. Shows sets/reps/rest timer information.
  - **Action:** `Close` or `Back` button to return to `Workout Detail`. Thumbs-down button to trigger the alternative exercise flow.

- **Screen: Nutrition Tab**
  - **Purpose:** To provide a clear, day-by-day view of the user's meal plan and access to recipes and grocery lists.
  - **Content Breakdown:**
    - **Header:** Date navigator with arrows and a calendar icon to select a day.
    - **Daily Meal List:** A vertical list of recipe cards, organized by meal type (Breakfast, Lunch, Dinner, Snacks).
    - **Recipe Cards:** Each card includes a meal image, name, key nutritional info, and a checkbox to mark as 'Eaten'.
    - **Grocery List Button:** A Floating Action Button to navigate to the consolidated weekly grocery list.
  - **Action:** Tapping a recipe card navigates to the `Recipe Detail` screen. Tapping the FAB opens the `Grocery List` screen.

- **Screen: Recipe Detail**
  - **Content:** Shows recipe image, ingredients, instructions, and nutritional information.
  - **Action:** Thumbs-down button to dislike the recipe. A checkbox to mark the meal as 'Eaten'.

---

### Flow 3: Logging Progress

This flow is centered around the `Progress` tab.

- **Screen: Progress (Dashboard)**
  - **Purpose:** To provide a motivational, data-rich view of the user's journey and results over time.
  - **Content Breakdown:**
    - **Header:** A time-frame selector to filter charts (e.g., 1M, 3M, 6M, All).
    - **Primary Chart:** An interactive line chart displaying weight over time.
    - **Secondary Metrics:** A grid of key stats like 'Total Workouts', 'Measurement Changes', and 'Current Streak'.
    - **Log Progress Button:** A Floating Action Button to open the progress logging modal.
  - **Action:** Tapping the FAB opens the `Log Progress` screen.

- **Screen: Log Progress (Modal or Full Screen)**
  - **Content:** A form with fields to input `Current Weight`, `Body Measurements`, and a text area for `How are you feeling?`. Option to upload a progress photo.
  - **Action:** `Save Log` button. `Cancel/Close` button.

---

### Flow 4: Profile & Settings Management

This flow is centered around the `Profile` tab.

- **Screen: Profile (Main)**
  - **Purpose:** To provide a central hub for all account management, personalization, and support-related tasks.
  - **Content Breakdown:**
    - **Header:** Displays the user's name, email, and a profile avatar.
    - **Navigation List:** A list of tappable items, each leading to a dedicated sub-screen:
      - `Edit Profile`: To update personal info and goals.
      - `Manage Subscription`: To view status and link to native subscription settings.
      - `Disliked Items`: To review and manage disliked content.
      - `Settings`: For app preferences like notifications and units.
      - `Get Support`: For help docs and contacting support.
      - `Logout`: A button to sign out.
  - **Action:** Tapping a list item navigates to the corresponding screen.

- **Screen: Disliked Items**
  - **Content:** Two lists: one for disliked exercises, one for disliked recipes.
  - **Action:** User can tap a 'remove' icon next to an item to take it off the disliked list, making it eligible for future plans.
