# Project Brief: Active Bite AI

## 1. Vision

To create a market-leading health and fitness application that leverages an agentic AI coach to provide deeply personalized and adaptive meal and workout plans, empowering users to achieve their health goals effectively.

## 2. Target Audience

- **Primary:** Individuals seeking a structured yet flexible approach to improving their health through nutrition and exercise.
- **Personas:** The app will cater to a wide range of users by creating detailed, evolving personas based on an initial questionnaire and ongoing progress tracking. This includes accommodating various dietary needs (allergies, preferences, etc.) and fitness levels (beginner to advanced).

## 3. Core Differentiators

- **Adaptive AI Coach:** The core of the app is an AI agent that proactively adjusts nutrition and training plans based on user progress, feedback, and goals. This creates a dynamic, truly personal coaching experience.
- **Negative Feedback Learning:** The AI will learn from explicit user feedback, such as disliking a recipe or being unable to perform an exercise, and will exclude these items from future plans.
- **Collective Intelligence:** With user consent, the AI will learn from anonymized, aggregate data across the user base to identify popular and effective recipes and workouts, improving recommendations for everyone.
- **Customizable AI Persona:** Users can select the personality of their AI coach (e.g., motivator, drill sergeant, data-focused) for a more engaging experience.

## 4. Feature Roadmap

### Minimum Viable Product (MVP) - v1.0

- **User Onboarding:** A multi-step, branched questionnaire (General > Nutrition/Workout) to create a detailed initial user persona.
- **AI Coach:** Initial version of the AI coach for generating meal and workout plans. Will suggest alternatives for disliked exercises.
- **Personalized Plans:** Custom daily/weekly meal and workout schedules.
- **Content Delivery:** In-house produced, instructional workout videos and human-reviewed, AI-generated recipes.
- **Feedback System:** Simple thumbs-up/down mechanism for meals and exercises, with a user-managed list of disliked items.
- **Progress Tracking:** Ability for users to log weight, body measurements, workout performance, and subjective feelings.
- **Grocery List:** Simple checklist functionality based on the meal plan.
- **Offline Access:** Caching of daily/weekly plans and optional video downloads for offline use.
- **Push Notifications:** Notification system for meal reminders, workout alerts, and progress updates.
- **User Authentication:** Secure login and profile management.
- **Admin Panel:** A comprehensive web-based dashboard for user management, content (video/recipe) management, subscription management, and a support ticket system.
- **Language:** English only and Spanish to begin with. Eventually other languages such as French, German, Italian, Portuguese, Russian, and Chinese.

### Post-MVP Roadmap

- **v1.1:** French language support.
- **v1.2:** Community features (progress sharing, challenges).
- **v1.3:** Integration with grocery delivery APIs.
- **v2.0:** Advanced features like wearable device integration, detailed analytics, and tiered subscription plans.

## 5. Technology & Architecture

- **Monorepo:** Turborepo
- **Backend:** Supabase (Database, Auth, Storage, Edge Functions)
- **Mobile App:** Expo (React Native) for iOS & Android
- **Admin Panel:** Next.js
- **AI Engine:** Gemini 2.5 Flash
- **State Management:** Zustand (for client-side state)
- **Analytics:** PostHog (for user behavior tracking)
- **Payment Processing:** Stripe (for subscription management)
- **Push Notifications:** Expo Notifications
- **Error Monitoring:** Sentry
- **CI/CD:** GitHub Actions
- **Code Quality:** ESLint & Prettier
- **Testing:** Jest & React Testing Library


## 6. Monetization

- **Initial Model:** $15/month subscription with a 7-day free trial.
- **Future Considerations:** Freemium model, tiered subscriptions, corporate wellness programs.

## 7. Data Privacy & Compliance

- The application will be designed for GDPR compliance from the ground up, ensuring user data is handled securely and transparently.
