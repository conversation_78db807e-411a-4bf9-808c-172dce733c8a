export declare const signUp: (email: string, password: string) => Promise<{
    data: {
        user: import("@supabase/supabase-js").AuthUser | null;
        session: import("@supabase/supabase-js").AuthSession | null;
    } | {
        user: null;
        session: null;
    };
    error: import("@supabase/supabase-js").AuthError | null;
}>;
export declare const signIn: (email: string, password: string) => Promise<{
    data: {
        user: import("@supabase/supabase-js").AuthUser;
        session: import("@supabase/supabase-js").AuthSession;
        weakPassword?: import("@supabase/supabase-js").WeakPassword;
    } | {
        user: null;
        session: null;
        weakPassword?: null;
    };
    error: import("@supabase/supabase-js").AuthError | null;
}>;
export declare const signOut: () => Promise<{
    error: import("@supabase/supabase-js").AuthError | null;
}>;
export declare const resetPassword: (email: string) => Promise<{
    data: {} | null;
    error: import("@supabase/supabase-js").AuthError | null;
}>;
export declare const updatePassword: (password: string) => Promise<{
    data: {
        user: import("@supabase/supabase-js").AuthUser;
    } | {
        user: null;
    };
    error: import("@supabase/supabase-js").AuthError | null;
}>;
export declare const getCurrentUser: () => Promise<{
    user: import("@supabase/supabase-js").AuthUser | null;
    error: import("@supabase/supabase-js").AuthError | null;
}>;
export declare const onAuthStateChange: (callback: (event: any, session: any) => void) => {
    data: {
        subscription: import("@supabase/supabase-js").Subscription;
    };
};
