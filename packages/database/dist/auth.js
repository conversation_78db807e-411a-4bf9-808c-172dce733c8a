import { supabase } from './client';
// Authentication functions
export const signUp = async (email, password) => {
    const { data, error } = await supabase.auth.signUp({
        email,
        password,
    });
    return { data, error };
};
export const signIn = async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
    });
    return { data, error };
};
export const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
};
export const resetPassword = async (email) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
    });
    return { data, error };
};
export const updatePassword = async (password) => {
    const { data, error } = await supabase.auth.updateUser({
        password,
    });
    return { data, error };
};
// Get current user
export const getCurrentUser = async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    return { user, error };
};
// Listen for auth state changes
export const onAuthStateChange = (callback) => {
    return supabase.auth.onAuthStateChange(callback);
};
