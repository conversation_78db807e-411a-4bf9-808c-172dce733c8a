import { describe, it, expect, beforeEach, vi } from 'vitest';
import { supabase } from './client';
import * as auth from './auth';
describe('Auth Service', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });
    it('should sign up a user', async () => {
        const mockData = {
            data: {
                user: { id: '123', email: '<EMAIL>' },
                session: { access_token: 'token' }
            },
            error: null
        };
        vi.mocked(supabase.auth.signUp).mockResolvedValue(mockData);
        const result = await auth.signUp('<EMAIL>', 'password123');
        expect(supabase.auth.signUp).toHaveBeenCalledWith({
            email: '<EMAIL>',
            password: 'password123',
        });
        expect(result.data).toEqual(mockData.data);
    });
    it('should sign in a user', async () => {
        const mockData = {
            data: {
                user: { id: '123', email: '<EMAIL>' },
                session: { access_token: 'token' }
            },
            error: null
        };
        vi.mocked(supabase.auth.signInWithPassword).mockResolvedValue(mockData);
        const result = await auth.signIn('<EMAIL>', 'password123');
        expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
            email: '<EMAIL>',
            password: 'password123',
        });
        expect(result.data).toEqual(mockData.data);
    });
    it('should sign out a user', async () => {
        const mockData = { error: null };
        vi.mocked(supabase.auth.signOut).mockResolvedValue(mockData);
        const result = await auth.signOut();
        expect(supabase.auth.signOut).toHaveBeenCalled();
        expect(result.error).toBeNull();
    });
});
