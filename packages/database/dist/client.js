import { createClient } from '@supabase/supabase-js';
// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables');
}
// Dev-only: log which Supabase project we are targeting (no secrets)
if ((process.env.NODE_ENV || '') !== 'production') {
    try {
        // eslint-disable-next-line no-console
        console.info('[Supabase] Project:', new URL(supabaseUrl).host);
    }
    catch {
        // ignore URL parse errors
    }
}
export const supabase = createClient(supabaseUrl, supabaseAnonKey);
