import type { Tables } from './types';
export declare function upsertOnboardingPreferences(userId: string, data: Partial<Pick<Tables<'onboarding_preferences'>, 'plan_selection' | 'ai_persona'>>): Promise<{
    data: Tables<"onboarding_preferences"> | null;
    error: import("@supabase/supabase-js").PostgrestError | null;
}>;
export declare function getOnboardingPreferences(userId: string): Promise<{
    data: Tables<"onboarding_preferences"> | null;
    error: import("@supabase/supabase-js").PostgrestError | null;
}>;
export declare function upsertOnboardingWorkout(userId: string, data: Partial<Pick<Tables<'onboarding_workout'>, 'experience_level' | 'known_injuries' | 'preferred_exercise_types' | 'available_equipment'>>): Promise<{
    data: Tables<"onboarding_workout"> | null;
    error: import("@supabase/supabase-js").PostgrestError | null;
}>;
export declare function upsertOnboardingNutrition(userId: string, data: Partial<Pick<Tables<'onboarding_nutrition'>, 'dietary_preferences' | 'allergies' | 'budget' | 'cuisine_types' | 'cooking_difficulty'>>): Promise<{
    data: Tables<"onboarding_nutrition"> | null;
    error: import("@supabase/supabase-js").PostgrestError | null;
}>;
export declare function upsertOnboardingGeneral(userId: string, data: Partial<Pick<Tables<'onboarding_general'>, 'age' | 'height' | 'weight'>>): Promise<{
    data: Tables<"onboarding_general"> | null;
    error: import("@supabase/supabase-js").PostgrestError | null;
}>;
