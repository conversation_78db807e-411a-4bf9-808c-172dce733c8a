import { supabase } from './client';
// Preferences
export async function upsertOnboardingPreferences(userId, data) {
    const payload = {
        user_id: userId,
        ...data,
        updated_at: new Date().toISOString(),
    };
    const { data: row, error } = await supabase
        .from('onboarding_preferences')
        .upsert(payload)
        .select('*')
        .single();
    return { data: row, error };
}
export async function getOnboardingPreferences(userId) {
    const { data, error } = await supabase
        .from('onboarding_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();
    return { data: data, error };
}
// Workout
export async function upsertOnboardingWorkout(userId, data) {
    const payload = {
        user_id: userId,
        ...data,
        updated_at: new Date().toISOString(),
    };
    const { data: row, error } = await supabase
        .from('onboarding_workout')
        .upsert(payload)
        .select('*')
        .single();
    return { data: row, error };
}
// Nutrition
export async function upsertOnboardingNutrition(userId, data) {
    const payload = {
        user_id: userId,
        ...data,
        updated_at: new Date().toISOString(),
    };
    const { data: row, error } = await supabase
        .from('onboarding_nutrition')
        .upsert(payload)
        .select('*')
        .single();
    return { data: row, error };
}
// General
export async function upsertOnboardingGeneral(userId, data) {
    const payload = {
        user_id: userId,
        ...data,
        updated_at: new Date().toISOString(),
    };
    const { data: row, error } = await supabase
        .from('onboarding_general')
        .upsert(payload)
        .select('*')
        .single();
    return { data: row, error };
}
