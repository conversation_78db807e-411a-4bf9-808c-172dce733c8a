import type { Tables } from './types';
export type Profile = Tables<'profiles'>;
export type ProfileUpdate = Partial<Pick<Profile, 'language' | 'units' | 'terms_accepted_at' | 'terms_version' | 'marketing_opt_in' | 'full_name' | 'avatar_url'>>;
export declare function getProfile(id: string): Promise<{
    data: Profile | null;
    error: import("@supabase/supabase-js").PostgrestError | null;
}>;
export declare function updateProfile(id: string, updates: ProfileUpdate): Promise<{
    data: Profile | null;
    error: import("@supabase/supabase-js").PostgrestError | null;
}>;
