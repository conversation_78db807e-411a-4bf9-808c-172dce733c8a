import { vi } from 'vitest';
// Mock environment variables before any imports
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
// Mock the client module directly
vi.mock('./client', () => ({
    supabase: {
        auth: {
            signUp: vi.fn().mockResolvedValue({ data: null, error: null }),
            signInWithPassword: vi.fn().mockResolvedValue({ data: null, error: null }),
            signOut: vi.fn().mockResolvedValue({ error: null }),
            resetPasswordForEmail: vi.fn().mockResolvedValue({ data: null, error: null }),
            updateUser: vi.fn().mockResolvedValue({ data: null, error: null }),
            getUser: vi.fn().mockResolvedValue({ data: { user: null }, error: null }),
            onAuthStateChange: vi.fn(),
        },
        from: vi.fn(() => ({
            select: vi.fn(),
            insert: vi.fn(),
            update: vi.fn(),
            delete: vi.fn(),
        })),
    },
}));
