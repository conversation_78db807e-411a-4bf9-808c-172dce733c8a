export type Json = string | number | boolean | null | {
    [key: string]: J<PERSON> | undefined;
} | Json[];
export type Database = {
    public: {
        Tables: {
            profiles: {
                Row: {
                    id: string;
                    updated_at: string | null;
                    username: string | null;
                    full_name: string | null;
                    avatar_url: string | null;
                    website: string | null;
                    language: string | null;
                    units: Database["public"]["Enums"]["units_enum"];
                    terms_accepted_at: string | null;
                    terms_version: string | null;
                    marketing_opt_in: boolean;
                };
                Insert: {
                    id: string;
                    updated_at?: string | null;
                    username?: string | null;
                    full_name?: string | null;
                    avatar_url?: string | null;
                    website?: string | null;
                    language?: string | null;
                    units?: Database["public"]["Enums"]["units_enum"];
                    terms_accepted_at?: string | null;
                    terms_version?: string | null;
                    marketing_opt_in?: boolean;
                };
                Update: {
                    id?: string;
                    updated_at?: string | null;
                    username?: string | null;
                    full_name?: string | null;
                    avatar_url?: string | null;
                    website?: string | null;
                    language?: string | null;
                    units?: Database["public"]["Enums"]["units_enum"];
                    terms_accepted_at?: string | null;
                    terms_version?: string | null;
                    marketing_opt_in?: boolean;
                };
                Relationships: [
                    {
                        foreignKeyName: "profiles_id_fkey";
                        columns: ["id"];
                        isOneToOne: true;
                        referencedRelation: "users";
                        referencedColumns: ["id"];
                    }
                ];
            };
            onboarding_preferences: {
                Row: {
                    user_id: string;
                    plan_selection: 'workout' | 'nutrition' | 'both' | null;
                    ai_persona: string | null;
                    updated_at: string | null;
                };
                Insert: {
                    user_id: string;
                    plan_selection?: 'workout' | 'nutrition' | 'both' | null;
                    ai_persona?: string | null;
                    updated_at?: string | null;
                };
                Update: {
                    user_id?: string;
                    plan_selection?: 'workout' | 'nutrition' | 'both' | null;
                    ai_persona?: string | null;
                    updated_at?: string | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "onboarding_preferences_user_id_fkey";
                        columns: ["user_id"];
                        isOneToOne: true;
                        referencedRelation: "profiles";
                        referencedColumns: ["id"];
                    }
                ];
            };
            onboarding_workout: {
                Row: {
                    user_id: string;
                    experience_level: string | null;
                    known_injuries: string[] | null;
                    preferred_exercise_types: string[] | null;
                    available_equipment: string[] | null;
                    updated_at: string | null;
                };
                Insert: {
                    user_id: string;
                    experience_level?: string | null;
                    known_injuries?: string[] | null;
                    preferred_exercise_types?: string[] | null;
                    available_equipment?: string[] | null;
                    updated_at?: string | null;
                };
                Update: {
                    user_id?: string;
                    experience_level?: string | null;
                    known_injuries?: string[] | null;
                    preferred_exercise_types?: string[] | null;
                    available_equipment?: string[] | null;
                    updated_at?: string | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "onboarding_workout_user_id_fkey";
                        columns: ["user_id"];
                        isOneToOne: true;
                        referencedRelation: "profiles";
                        referencedColumns: ["id"];
                    }
                ];
            };
            onboarding_nutrition: {
                Row: {
                    user_id: string;
                    dietary_preferences: string[] | null;
                    allergies: string[] | null;
                    budget: string | null;
                    cuisine_types: string[] | null;
                    cooking_difficulty: string | null;
                    updated_at: string | null;
                };
                Insert: {
                    user_id: string;
                    dietary_preferences?: string[] | null;
                    allergies?: string[] | null;
                    budget?: string | null;
                    cuisine_types?: string[] | null;
                    cooking_difficulty?: string | null;
                    updated_at?: string | null;
                };
                Update: {
                    user_id?: string;
                    dietary_preferences?: string[] | null;
                    allergies?: string[] | null;
                    budget?: string | null;
                    cuisine_types?: string[] | null;
                    cooking_difficulty?: string | null;
                    updated_at?: string | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "onboarding_nutrition_user_id_fkey";
                        columns: ["user_id"];
                        isOneToOne: true;
                        referencedRelation: "profiles";
                        referencedColumns: ["id"];
                    }
                ];
            };
            onboarding_general: {
                Row: {
                    user_id: string;
                    age: number | null;
                    height: number | null;
                    weight: number | null;
                    updated_at: string | null;
                };
                Insert: {
                    user_id: string;
                    age?: number | null;
                    height?: number | null;
                    weight?: number | null;
                    updated_at?: string | null;
                };
                Update: {
                    user_id?: string;
                    age?: number | null;
                    height?: number | null;
                    weight?: number | null;
                    updated_at?: string | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "onboarding_general_user_id_fkey";
                        columns: ["user_id"];
                        isOneToOne: true;
                        referencedRelation: "profiles";
                        referencedColumns: ["id"];
                    }
                ];
            };
        };
        Views: {
            [_ in never]: never;
        };
        Functions: {
            [_ in never]: never;
        };
        Enums: {
            units_enum: "metric" | "imperial";
        };
        CompositeTypes: {
            [_ in never]: never;
        };
    };
};
export type Tables<PublicTableNameOrOptions extends keyof (Database["public"]["Tables"] & Database["public"]["Views"]) | {
    schema: keyof Database;
}, TableName extends PublicTableNameOrOptions extends {
    schema: keyof Database;
} ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] & Database[PublicTableNameOrOptions["schema"]]["Views"]) : never = never> = PublicTableNameOrOptions extends {
    schema: keyof Database;
} ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] & Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
    Row: infer R;
} ? R : never : PublicTableNameOrOptions extends keyof (Database["public"]["Tables"] & Database["public"]["Views"]) ? (Database["public"]["Tables"] & Database["public"]["Views"])[PublicTableNameOrOptions] extends {
    Row: infer R;
} ? R : never : never;
export type TablesInsert<PublicTableNameOrOptions extends keyof Database["public"]["Tables"] | {
    schema: keyof Database;
}, TableName extends PublicTableNameOrOptions extends {
    schema: keyof Database;
} ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"] : never = never> = PublicTableNameOrOptions extends {
    schema: keyof Database;
} ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Insert: infer I;
} ? I : never : PublicTableNameOrOptions extends keyof Database["public"]["Tables"] ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
    Insert: infer I;
} ? I : never : never;
export type TablesUpdate<PublicTableNameOrOptions extends keyof Database["public"]["Tables"] | {
    schema: keyof Database;
}, TableName extends PublicTableNameOrOptions extends {
    schema: keyof Database;
} ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"] : never = never> = PublicTableNameOrOptions extends {
    schema: keyof Database;
} ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Update: infer U;
} ? U : never : PublicTableNameOrOptions extends keyof Database["public"]["Tables"] ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
    Update: infer U;
} ? U : never : never;
export type Enums<PublicEnumNameOrOptions extends keyof Database["public"]["Enums"] | {
    schema: keyof Database;
}, EnumName extends PublicEnumNameOrOptions extends {
    schema: keyof Database;
} ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"] : never = never> = PublicEnumNameOrOptions extends {
    schema: keyof Database;
} ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName] : PublicEnumNameOrOptions extends keyof Database["public"]["Enums"] ? Database["public"]["Enums"][PublicEnumNameOrOptions] : never;
