# Proposed Minimal Onboarding Schema (Supabase)

This schema captures the onboarding fields from the mobile app's OnboardingData. It keeps user profile table focused and stores questionnaire-like data in separate tables.

## Tables

1. profiles (existing)
- id uuid PK (auth.users.id)
- language text
- units text
- terms_accepted_at timestamptz
- terms_version text
- marketing_opt_in boolean

2. onboarding_preferences
- user_id uuid PK, FK -> profiles.id (ON DELETE CASCADE)
- plan_selection text check in ('workout','nutrition','both')
- ai_persona text
- updated_at timestamptz default now()

3. onboarding_workout
- user_id uuid PK, FK -> profiles.id
- experience_level text
- known_injuries text[]
- preferred_exercise_types text[]
- available_equipment text[]
- updated_at timestamptz default now()

4. onboarding_nutrition
- user_id uuid PK, FK -> profiles.id
- dietary_preferences text[]
- allergies text[]
- budget text
- cuisine_types text[]
- cooking_difficulty text
- updated_at timestamptz default now()

5. onboarding_general
- user_id uuid PK, FK -> profiles.id
- age int
- height numeric
- weight numeric
- updated_at timestamptz default now()

## Notes
- Keep RLS enabled with user_id = auth.uid() policy on onboarding_* tables.
- Use upsert semantics from mobile after auth to merge local onboarding storage.
- Future: factor to normalized reference tables for choices; capture timestamps for completion milestones.

