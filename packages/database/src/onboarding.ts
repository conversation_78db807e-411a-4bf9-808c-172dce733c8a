import { supabase } from './client'
import type { Tables, TablesInsert } from './types'

// Preferences
export async function upsertOnboardingPreferences(
  userId: string,
  data: Partial<Pick<Tables<'onboarding_preferences'>, 'plan_selection' | 'ai_persona'>>
) {
  const payload: TablesInsert<'onboarding_preferences'> = {
    user_id: userId,
    ...data,
    updated_at: new Date().toISOString(),
  }
  const { data: row, error } = await supabase
    .from('onboarding_preferences')
    .upsert(payload)
    .select('*')
    .single()
  return { data: row as Tables<'onboarding_preferences'> | null, error }
}

export async function getOnboardingPreferences(userId: string) {
  const { data, error } = await supabase
    .from('onboarding_preferences')
    .select('*')
    .eq('user_id', userId)
    .single()
  return { data: data as Tables<'onboarding_preferences'> | null, error }
}

// Workout
export async function upsertOnboardingWorkout(
  userId: string,
  data: Partial<Pick<Tables<'onboarding_workout'>, 'experience_level' | 'known_injuries' | 'preferred_exercise_types' | 'available_equipment'>>
) {
  const payload: TablesInsert<'onboarding_workout'> = {
    user_id: userId,
    ...data,
    updated_at: new Date().toISOString(),
  }
  const { data: row, error } = await supabase
    .from('onboarding_workout')
    .upsert(payload)
    .select('*')
    .single()
  return { data: row as Tables<'onboarding_workout'> | null, error }
}

// Nutrition
export async function upsertOnboardingNutrition(
  userId: string,
  data: Partial<Pick<Tables<'onboarding_nutrition'>, 'dietary_preferences' | 'allergies' | 'budget' | 'cuisine_types' | 'cooking_difficulty'>>
) {
  const payload: TablesInsert<'onboarding_nutrition'> = {
    user_id: userId,
    ...data,
    updated_at: new Date().toISOString(),
  }
  const { data: row, error } = await supabase
    .from('onboarding_nutrition')
    .upsert(payload)
    .select('*')
    .single()
  return { data: row as Tables<'onboarding_nutrition'> | null, error }
}

// General
export async function upsertOnboardingGeneral(
  userId: string,
  data: Partial<Pick<Tables<'onboarding_general'>, 'age' | 'height' | 'weight'>>
) {
  const payload: TablesInsert<'onboarding_general'> = {
    user_id: userId,
    ...data,
    updated_at: new Date().toISOString(),
  }
  const { data: row, error } = await supabase
    .from('onboarding_general')
    .upsert(payload)
    .select('*')
    .single()
  return { data: row as Tables<'onboarding_general'> | null, error }
}

