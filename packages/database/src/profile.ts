import { supabase } from './client'
import type { Tables, Database } from './types'

export type Profile = Tables<'profiles'>
export type ProfileUpdate = Partial<Pick<Profile,
  'language' | 'units' | 'terms_accepted_at' | 'terms_version' | 'marketing_opt_in' | 'full_name' | 'avatar_url'
>>

export async function getProfile(id: string) {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', id)
    .single()
  return { data: data as Profile | null, error }
}

export async function updateProfile(id: string, updates: ProfileUpdate) {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates as Database['public']['Tables']['profiles']['Update'])
    .eq('id', id)
    .select('*')
    .single()
  return { data: data as Profile | null, error }
}
