export declare const colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    error: string;
    success: string;
    warning: string;
};
export declare const spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
};
export declare const typography: {
    fontFamily: {
        primary: string;
    };
    fontSize: {
        xs: number;
        sm: number;
        base: number;
        lg: number;
        xl: number;
        '2xl': number;
        '3xl': number;
        '4xl': number;
    };
    fontWeight: {
        normal: string;
        medium: string;
        semibold: string;
        bold: string;
    };
};
export declare const borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
    full: number;
};
export declare const shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
};
export declare const zIndex: {
    auto: string;
    base: number;
    dropdown: number;
    sticky: number;
    fixed: number;
    modal: number;
    popover: number;
    tooltip: number;
};
declare const _default: {
    colors: {
        primary: string;
        secondary: string;
        accent: string;
        background: string;
        surface: string;
        text: string;
        textSecondary: string;
        border: string;
        error: string;
        success: string;
        warning: string;
    };
    spacing: {
        xs: number;
        sm: number;
        md: number;
        lg: number;
        xl: number;
        xxl: number;
    };
    typography: {
        fontFamily: {
            primary: string;
        };
        fontSize: {
            xs: number;
            sm: number;
            base: number;
            lg: number;
            xl: number;
            '2xl': number;
            '3xl': number;
            '4xl': number;
        };
        fontWeight: {
            normal: string;
            medium: string;
            semibold: string;
            bold: string;
        };
    };
    borderRadius: {
        sm: number;
        md: number;
        lg: number;
        xl: number;
        full: number;
    };
    shadows: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
    };
    zIndex: {
        auto: string;
        base: number;
        dropdown: number;
        sticky: number;
        fixed: number;
        modal: number;
        popover: number;
        tooltip: number;
    };
};
export default _default;
